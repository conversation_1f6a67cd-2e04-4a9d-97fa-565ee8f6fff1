// User, account, role and authentication related schemas
import { relations } from "drizzle-orm";
import {
  boolean,
  index,
  integer,
  pgEnum,
  pgTable,
  uuid as pgUuid,
  primaryKey,
  serial,
  text,
  timestamp,
  unique,
  varchar,
} from "drizzle-orm/pg-core";
import { appPermissionsEnum } from "./shared";

// Users table
export const users = pgTable(
  "users",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    email: varchar("email", { length: 255 }).notNull().unique(),
    name: varchar("name", { length: 255 }),
    avatar: text("avatar"),
    emailVerified: boolean("email_verified").default(false).notNull(),
    inviteCode: varchar("invite_code", { length: 10 }).unique(),
    invitedBy: pgUuid("invited_by").references(() => users.id),
    credits: integer("credits").default(0).notNull(),
    onboardingCompleted: boolean("onboarding_completed").default(false).notNull(),
    onboardingStep: varchar("onboarding_step", { length: 50 }),
    preferences: text("preferences"), // JSON string for user preferences
    lastLoginAt: timestamp("last_login_at"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    emailIdx: index("users_email_idx").on(table.email),
    inviteCodeIdx: index("users_invite_code_idx").on(table.inviteCode),
    invitedByIdx: index("users_invited_by_idx").on(table.invitedBy),
  })
);

// Accounts table for OAuth providers
export const accounts = pgTable(
  "accounts",
  {
    id: serial("id").primaryKey(),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    provider: varchar("provider", { length: 255 }).notNull(),
    providerUserId: varchar("provider_user_id", { length: 255 }).notNull(),
    accessToken: text("access_token"),
    refreshToken: text("refresh_token"),
    expiresAt: timestamp("expires_at"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userProviderIdx: unique().on(table.userId, table.provider),
    providerUserIdx: index("accounts_provider_user_idx").on(table.provider, table.providerUserId),
  })
);

// Roles table
export const roles = pgTable("roles", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull().unique(),
  description: text("description"),
});

// Role permissions junction table
export const rolePermissions = pgTable(
  "role_permissions",
  {
    roleId: integer("role_id")
      .notNull()
      .references(() => roles.id, { onDelete: "cascade" }),
    permission: appPermissionsEnum("permission").notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.roleId, table.permission] }),
  })
);

// Account memberships (users can have roles in accounts)
export const accountsMemberships = pgTable(
  "accounts_memberships",
  {
    id: serial("id").primaryKey(),
    accountId: pgUuid("account_id")
      .notNull()
      .references(() => accounts.id, { onDelete: "cascade" }),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    roleId: integer("role_id")
      .notNull()
      .references(() => roles.id),
    invitedBy: pgUuid("invited_by").references(() => users.id),
    invitedAt: timestamp("invited_at"),
    joinedAt: timestamp("joined_at"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    accountUserIdx: unique().on(table.accountId, table.userId),
    userIdx: index("memberships_user_idx").on(table.userId),
    roleIdx: index("memberships_role_idx").on(table.roleId),
  })
);

// Sessions table
export const sessions = pgTable(
  "sessions",
  {
    id: varchar("id", { length: 255 }).primaryKey(),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    expiresAt: timestamp("expires_at").notNull(),
    userAgent: text("user_agent"),
    ipAddress: varchar("ip_address", { length: 45 }),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdx: index("sessions_user_idx").on(table.userId),
    expiresIdx: index("sessions_expires_idx").on(table.expiresAt),
  })
);

// Invitations table
export const invitations = pgTable(
  "invitations",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    email: varchar("email", { length: 255 }).notNull(),
    roleId: integer("role_id")
      .notNull()
      .references(() => roles.id),
    invitedBy: pgUuid("invited_by")
      .notNull()
      .references(() => users.id),
    token: varchar("token", { length: 255 }).notNull().unique(),
    expiresAt: timestamp("expires_at").notNull(),
    acceptedAt: timestamp("accepted_at"),
    acceptedBy: pgUuid("accepted_by").references(() => users.id),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    tokenIdx: index("invitations_token_idx").on(table.token),
    emailIdx: index("invitations_email_idx").on(table.email),
    expiresIdx: index("invitations_expires_idx").on(table.expiresAt),
  })
);

// Notification enums
export const notificationTypeEnum = pgEnum("notification_type", [
  "system",
  "billing",
  "security",
  "feature",
  "marketing",
]);

export const notificationChannelEnum = pgEnum("notification_channel", [
  "in_app",
  "email",
  "push",
  "sms",
]);

// Notifications table
export const notifications = pgTable(
  "notifications",
  {
    id: serial("id").primaryKey(),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    title: varchar("title", { length: 255 }).notNull(),
    body: text("body").notNull(),
    type: notificationTypeEnum("type").notNull().default("system"),
    channel: notificationChannelEnum("channel").notNull().default("in_app"),
    link: varchar("link", { length: 500 }),
    dismissed: boolean("dismissed").default(false).notNull(),
    readAt: timestamp("read_at"),
    expiresAt: timestamp("expires_at"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdx: index("notifications_user_idx").on(table.userId),
    typeIdx: index("notifications_type_idx").on(table.type),
    channelIdx: index("notifications_channel_idx").on(table.channel),
    dismissedIdx: index("notifications_dismissed_idx").on(table.dismissed),
    readIdx: index("notifications_read_idx").on(table.readAt),
    expiresIdx: index("notifications_expires_idx").on(table.expiresAt),
    createdIdx: index("notifications_created_idx").on(table.createdAt),
  })
);

// API Keys table
export const apiKeys = pgTable(
  "api_keys",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    name: varchar("name", { length: 255 }).notNull(),
    keyHash: varchar("key_hash", { length: 255 }).notNull().unique(),
    lastUsedAt: timestamp("last_used_at"),
    expiresAt: timestamp("expires_at"),
    isActive: boolean("is_active").default(true).notNull(),
    permissions: text("permissions"), // JSON array of permissions
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdx: index("api_keys_user_idx").on(table.userId),
    keyHashIdx: index("api_keys_hash_idx").on(table.keyHash),
    activeIdx: index("api_keys_active_idx").on(table.isActive),
  })
);

// Relations
export const usersRelations = relations(users, ({ many, one }) => ({
  accounts: many(accounts),
  sessions: many(sessions),
  memberships: many(accountsMemberships),
  invitations: many(invitations),
  notifications: many(notifications),
  apiKeys: many(apiKeys),
  invitedBy: one(users, {
    fields: [users.invitedBy],
    references: [users.id],
  }),
  invitedUsers: many(users),
}));

export const accountsRelations = relations(accounts, ({ one, many }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.id],
  }),
  memberships: many(accountsMemberships),
}));

export const rolesRelations = relations(roles, ({ many }) => ({
  permissions: many(rolePermissions),
  memberships: many(accountsMemberships),
  invitations: many(invitations),
}));

export const rolePermissionsRelations = relations(rolePermissions, ({ one }) => ({
  role: one(roles, {
    fields: [rolePermissions.roleId],
    references: [roles.id],
  }),
}));

export const accountsMembershipsRelations = relations(accountsMemberships, ({ one }) => ({
  account: one(accounts, {
    fields: [accountsMemberships.accountId],
    references: [accounts.id],
  }),
  user: one(users, {
    fields: [accountsMemberships.userId],
    references: [users.id],
  }),
  role: one(roles, {
    fields: [accountsMemberships.roleId],
    references: [roles.id],
  }),
  invitedBy: one(users, {
    fields: [accountsMemberships.invitedBy],
    references: [users.id],
  }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

export const invitationsRelations = relations(invitations, ({ one }) => ({
  role: one(roles, {
    fields: [invitations.roleId],
    references: [roles.id],
  }),
  invitedBy: one(users, {
    fields: [invitations.invitedBy],
    references: [users.id],
  }),
  acceptedBy: one(users, {
    fields: [invitations.acceptedBy],
    references: [users.id],
  }),
}));

export const notificationsRelations = relations(notifications, ({ one }) => ({
  user: one(users, {
    fields: [notifications.userId],
    references: [users.id],
  }),
}));

export const apiKeysRelations = relations(apiKeys, ({ one }) => ({
  user: one(users, {
    fields: [apiKeys.userId],
    references: [users.id],
  }),
}));
