// API response utilities (moved from app/lib/api/resp.ts)
import { json } from "@remix-run/node";

/**
 * Standard API response structure
 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  error?: string;
}

/**
 * Success response with data
 */
export function respData<T>(data: T, message = "success") {
  return json<ApiResponse<T>>({
    code: 0,
    message,
    data,
  });
}

/**
 * Error response with proper HTTP status codes
 */
export function respErr(message: string, code = -1, httpStatus = 400) {
  return json<ApiResponse>(
    {
      code,
      message,
      error: message,
    },
    { status: httpStatus }
  );
}

/**
 * Success response without data
 */
export function respOk(message = "success") {
  return json<ApiResponse>({
    code: 0,
    message,
  });
}

/**
 * Custom response with specific code and message
 */
export function respJson<T>(code: number, message: string, data?: T, httpStatus?: number) {
  const status = httpStatus || (code === 0 ? 200 : code === -2 ? 401 : 400);
  return json<ApiResponse<T>>(
    {
      code,
      message,
      data,
    },
    { status }
  );
}

/**
 * Server error response
 */
export function respServerErr(message = "Internal server error") {
  return json<ApiResponse>(
    {
      code: -500,
      message,
      error: message,
    },
    { status: 500 }
  );
}

/**
 * Unauthorized response
 */
export function respUnauthorized(message = "Unauthorized") {
  return json<ApiResponse>(
    {
      code: -401,
      message,
      error: message,
    },
    { status: 401 }
  );
}

/**
 * Forbidden response
 */
export function respForbidden(message = "Forbidden") {
  return json<ApiResponse>(
    {
      code: -403,
      message,
      error: message,
    },
    { status: 403 }
  );
}

/**
 * Not found response
 */
export function respNotFound(message = "Not found") {
  return json<ApiResponse>(
    {
      code: -404,
      message,
      error: message,
    },
    { status: 404 }
  );
}

/**
 * Validation error response
 */
export function respValidationErr(message: string, errors?: Record<string, string[]>) {
  return json<ApiResponse>(
    {
      code: -422,
      message,
      error: message,
      data: errors,
    },
    { status: 422 }
  );
}

/**
 * Type guard to check if response is successful
 */
export function isSuccessResponse<T>(
  response: ApiResponse<T>
): response is ApiResponse<T> & { data: T } {
  return response.code === 0 && response.data !== undefined;
}

/**
 * Type guard to check if response is an error
 */
export function isErrorResponse(response: ApiResponse): boolean {
  return response.code !== 0;
}
