// Re-export monitoring related modules for easier imports
export * from "./analytics";
// Server-side monitoring modules
export * from "./error-tracker.server";
export * from "./log-aggregator.server";
export * from "./performance";
export * from "./system-monitor.server";

// Common monitoring types and utilities
export interface MonitoringConfig {
  enabled: boolean;
  errorTracking: {
    enabled: boolean;
    sampleRate: number;
    retentionDays: number;
  };
  performance: {
    enabled: boolean;
    slowRequestThreshold: number; // milliseconds
    metricsRetentionDays: number;
  };
  logging: {
    level: "error" | "warning" | "info" | "debug";
    retentionDays: number;
    enableAggregation: boolean;
  };
  alerts: {
    enabled: boolean;
    channels: ("email" | "webhook" | "database")[];
    thresholds: {
      errorRate: number; // percentage
      responseTime: number; // milliseconds
      memoryUsage: number; // percentage
    };
  };
}

export const DEFAULT_MONITORING_CONFIG: MonitoringConfig = {
  enabled: true,
  errorTracking: {
    enabled: true,
    sampleRate: 1.0,
    retentionDays: 30,
  },
  performance: {
    enabled: true,
    slowRequestThreshold: 1000,
    metricsRetentionDays: 30,
  },
  logging: {
    level: "info",
    retentionDays: 7,
    enableAggregation: true,
  },
  alerts: {
    enabled: true,
    channels: ["database"],
    thresholds: {
      errorRate: 5.0,
      responseTime: 2000,
      memoryUsage: 85.0,
    },
  },
};

// Monitoring utilities
export function isMonitoringEnabled(config: MonitoringConfig): boolean {
  return config.enabled;
}

export function shouldTrackError(config: MonitoringConfig): boolean {
  return config.enabled && config.errorTracking.enabled;
}

export function shouldTrackPerformance(config: MonitoringConfig): boolean {
  return config.enabled && config.performance.enabled;
}

export function isSlowRequest(responseTime: number, config: MonitoringConfig): boolean {
  return responseTime > config.performance.slowRequestThreshold;
}
