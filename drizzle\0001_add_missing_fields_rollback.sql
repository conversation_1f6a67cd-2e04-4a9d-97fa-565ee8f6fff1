-- Rollback Migration: Remove added fields
-- Created: 2025-01-23
-- Description: Rollback script for 0001_add_missing_fields.sql

-- Drop foreign key constraints first
ALTER TABLE "accounts" DROP CONSTRAINT IF EXISTS "accounts_user_id_users_id_fk";
ALTER TABLE "notifications" DROP CONSTRAINT IF EXISTS "notifications_account_id_accounts_id_fk";
ALTER TABLE "subscriptions" DROP CONSTRAINT IF EXISTS "subscriptions_account_id_accounts_id_fk";

-- Drop indexes
DROP INDEX IF EXISTS "orders_order_no_idx";
DROP INDEX IF EXISTS "orders_user_email_idx";
DROP INDEX IF EXISTS "orders_user_uuid_idx";
DROP INDEX IF EXISTS "accounts_user_id_idx";
DROP INDEX IF EXISTS "accounts_provider_idx";
DROP INDEX IF EXISTS "credit_transactions_trans_no_idx";
DROP INDEX IF EXISTS "notifications_account_id_idx";
DROP INDEX IF EXISTS "subscriptions_account_id_idx";
DROP INDEX IF EXISTS "sessions_session_token_idx";

-- Drop unique constraints
ALTER TABLE "orders" DROP CONSTRAINT IF EXISTS "orders_order_no_unique";
ALTER TABLE "credit_transactions" DROP CONSTRAINT IF EXISTS "credit_transactions_trans_no_unique";

-- Remove columns from sessions table
ALTER TABLE "sessions" DROP COLUMN IF EXISTS "session_token";
ALTER TABLE "sessions" DROP COLUMN IF EXISTS "is_active";

-- Remove columns from subscriptions table
ALTER TABLE "subscriptions" DROP COLUMN IF EXISTS "account_id";
ALTER TABLE "subscriptions" DROP COLUMN IF EXISTS "stripe_subscription_id";
ALTER TABLE "subscriptions" DROP COLUMN IF EXISTS "stripe_customer_id";

-- Remove columns from notifications table
ALTER TABLE "notifications" DROP COLUMN IF EXISTS "account_id";

-- Remove columns from credit_transactions table
ALTER TABLE "credit_transactions" DROP COLUMN IF EXISTS "trans_no";

-- Remove columns from orders table
ALTER TABLE "orders" DROP COLUMN IF EXISTS "order_no";
ALTER TABLE "orders" DROP COLUMN IF EXISTS "billing_provider";
ALTER TABLE "orders" DROP COLUMN IF EXISTS "user_email";
ALTER TABLE "orders" DROP COLUMN IF EXISTS "user_uuid";

-- Remove columns from accounts table
ALTER TABLE "accounts" DROP COLUMN IF EXISTS "user_id";
ALTER TABLE "accounts" DROP COLUMN IF EXISTS "provider";
ALTER TABLE "accounts" DROP COLUMN IF EXISTS "provider_user_id";
ALTER TABLE "accounts" DROP COLUMN IF EXISTS "access_token";
ALTER TABLE "accounts" DROP COLUMN IF EXISTS "refresh_token";
ALTER TABLE "accounts" DROP COLUMN IF EXISTS "expires_at";
