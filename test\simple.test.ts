import { describe, expect, it } from "vitest";

// Very simple test to verify vitest is working
describe("Simple Test", () => {
  it("should pass basic assertion", () => {
    expect(1 + 1).toBe(2);
  });

  it("should validate string operations", () => {
    const str = "hello world";
    expect(str.toUpperCase()).toBe("HELLO WORLD");
    expect(str.length).toBe(11);
  });

  it("should validate array operations", () => {
    const arr = [1, 2, 3];
    expect(arr.length).toBe(3);
    expect(arr.includes(2)).toBe(true);
    expect(arr.includes(4)).toBe(false);
  });

  it("should validate object operations", () => {
    const obj = { name: "test", value: 42 };
    expect(obj.name).toBe("test");
    expect(obj.value).toBe(42);
    expect(Object.keys(obj)).toEqual(["name", "value"]);
  });
});
