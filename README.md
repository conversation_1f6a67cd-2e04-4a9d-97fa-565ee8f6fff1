# Welcome to Remix + Vercel!

- 📖 [Remix docs](https://remix.run/docs)
- 📖 [Remix Vercel docs](https://remix.run/guides/vite#vercel)

## Package Manager

This project uses **pnpm** as the package manager. Make sure you have pnpm installed:

```sh
npm install -g pnpm
# or
corepack enable
```

## Development

Run the dev server:

```sh
pnpm run dev
```

To run the production build locally:

```sh
pnpm run build
pnpm run start
```

## Deployment

If you don't already have an account, then [create a Vercel account here](https://vercel.com/signup).

Once that's done, you should be able to deploy your app:

```sh
pnpm run deploy
```

## Database (Neon + Drizzle ORM)

This template is configured with Neon PostgreSQL database and Drizzle ORM.

### Setup

1. Create a Neon database account at [neon.tech](https://neon.tech)
2. Create a new database and get the connection string
3. Copy `.dev.vars.example` to `.dev.vars` and add your database URL:
   ```
   DATABASE_URL="****************************************************************"
   ```

### Database Commands

```sh
# Generate migration files
pnpm run db:generate

# Push schema changes to database (for development)
pnpm run db:push

# Run migrations
pnpm run db:migrate

# Open Drizzle Studio (database GUI)
pnpm run db:studio
```

### Test Database Connection

Visit `/test-db` to test your database connection.

## Styling

This template comes with [Tailwind CSS](https://tailwindcss.com/) already configured for a simple default starting experience. You can use whatever css framework you prefer. See the [Vite docs on css](https://vitejs.dev/guide/features.html#css) for more information.

## State Management (Zustand)

This project includes [Zustand](https://github.com/pmndrs/zustand) for simple and fast state management.

### Features

- 🚀 **Lightweight**: Only 2.9kb (gzipped)
- 🔥 **Fast**: React hooks-based with excellent performance
- 🛠 **TypeScript Support**: Full type safety
- 💾 **Persistence**: localStorage persistence support
- 🔄 **SSR Compatible**: Server-side rendering support
- 🧪 **Easy Testing**: Simple testing API

### Quick Start

```tsx
import { useUserStore, useUserActions } from '~/stores';

function UserProfile() {
  const user = useUserStore((state) => state.user);
  const { setUser, clearUser } = useUserActions();

  return (
    <div>
      {user ? (
        <div>
          <p>Welcome, {user.name}!</p>
          <button onClick={clearUser}>Logout</button>
        </div>
      ) : (
        <button onClick={() => setUser({ id: '1', name: 'John', email: '<EMAIL>' })}>
          Login
        </button>
      )}
    </div>
  );
}
```

### Available Stores

- **User Store**: Authentication and user data
- **UI Store**: Theme, language, notifications
- **Cart Store**: Shopping cart functionality
- **App Store**: Global application state

### Demo

Visit `/zustand` to see a comprehensive demo of all Zustand features.

For detailed documentation, see [app/stores/README.md](app/stores/README.md).

## Google Analytics Integration

This project includes an integration with Google Analytics 4 (GA4) to enable website analytics and user behavior tracking.

### Setup

1.  **Configure Tracking ID**:
    You need to provide your Google Analytics Measurement ID (e.g., `G-XXXXXXXXXX`) as an environment variable.

    *   **For production (Cloudflare Workers):**
        Open `wrangler.toml` and set the `GA_TRACKING_ID` variable:
        ```toml
        [vars]
        # ... other vars
        GA_TRACKING_ID = "G-YOUR_MEASUREMENT_ID"
        ```

    *   **For local development:**
        Create or open the `.dev.vars` file (copied from `.dev.vars.example`) in the root of the project and set:
        ```
        GA_TRACKING_ID="G-YOUR_MEASUREMENT_ID"
        ```
        If `GA_TRACKING_ID` is not provided, Google Analytics tracking will be disabled.

### Features

*   **Page View Tracking**: Automatically tracks page views on route changes. This is handled in `app/root.tsx`.
*   **Custom Event Tracking**: You can track custom events using the `event` function from `app/lib/analytics.ts`.

    **Example of Custom Event Tracking:**
    The `app/components/dev/ga-event-example.tsx` component demonstrates how to track a custom event:
    ```typescript
    import { event } from "~/lib/analytics";

    // Inside your component or event handler
    event({
      action: "button_click",
      category: "User Interaction",
      label: "Specific Button Clicked",
      value: 1, // Optional
    });
    ```
    You can see this example in action on the `/components` route.

### GDPR & Consent Management

The Google Analytics integration is set up with a default consent configuration to support GDPR compliance. By default, `analytics_storage` and other tracking-related storage options are set to `'denied'`.

This means **no tracking will occur unless explicit consent is granted by the user**.

To enable tracking, you will need to integrate a Consent Management Platform (CMP) or implement your own consent banner that updates the consent states using the `gtag('consent', 'update', { ... })` command.

For example, if a user grants consent for analytics:
```javascript
// This code would typically be run after user interaction with a consent banner
if (typeof window.gtag === 'function') {
  window.gtag('consent', 'update', {
    'analytics_storage': 'granted'
  });
}
```

This setup ensures that the application respects user privacy choices by default. It is your responsibility to implement the mechanism for collecting and managing user consent.

## S3-Compatible Storage

This project includes a module for interacting with S3-compatible object storage services, such as AWS S3, Cloudflare R2, MinIO, etc. This allows you to upload, download, and manage files.

### Overview

The storage library (`app/lib/storage.ts`) provides a `Storage` class with methods to:
- Upload files directly (e.g., from a Buffer).
- Download files from a remote URL and then upload them to your storage.
- Generate public URLs for uploaded files, using either a custom domain or pre-signed URLs.

### Configuration

To use the S3-compatible storage, you need to configure the following environment variables. These should be added to your `.dev.vars` file for local development with Cloudflare Workers, or to your environment variable configuration for deployed environments.

-   `STORAGE_ENDPOINT`: The S3 API endpoint of your storage provider.
-   `STORAGE_REGION`: The region of your storage bucket (e.g., `us-east-1`, `auto`).
-   `STORAGE_ACCESS_KEY`: Your S3 access key ID.
-   `STORAGE_SECRET_KEY`: Your S3 secret access key.
-   `STORAGE_BUCKET`: The name of the bucket you want to use.
-   `STORAGE_DOMAIN` (Optional): A custom domain (e.g., a CDN domain) that serves files from your bucket. If provided, public URLs will use this domain. Otherwise, pre-signed URLs will be generated.

Example for `.dev.vars`:
```env
STORAGE_ENDPOINT="your-s3-endpoint"
STORAGE_REGION="your-region"
STORAGE_ACCESS_KEY="your-access-key"
STORAGE_SECRET_KEY="your-secret-key"
STORAGE_BUCKET="your-bucket-name"
STORAGE_DOMAIN="your-custom-domain.com" # Optional
```

### Usage

**1. Initialize the Service:**
Import `newStorage` and create an instance. It automatically picks up the environment variables.

```typescript
import { newStorage } from "~/lib/storage";

const storage = newStorage();
```

**2. Upload a File (from Buffer):**

```typescript
import { newStorage } from "~/lib/storage";
// ...

async function handleUpload(fileBuffer: Buffer, filename: string) {
  const storage = newStorage();
  try {
    const result = await storage.uploadFile({
      body: fileBuffer,
      key: `uploads/${filename}`, // Desired path/key in the bucket
      contentType: "image/png", // Or any other appropriate content type
      disposition: "inline", // Or "attachment" to force download
    });
    console.log("Upload successful:", result.url);
    return result;
  } catch (error) {
    console.error("Upload failed:", error);
  }
}
```

**3. Download from URL and Upload:**

```typescript
import { newStorage } from "~/lib/storage";
// ...

async function downloadAndStoreImage(imageUrl: string, targetFilename: string) {
  const storage = newStorage();
  try {
    const result = await storage.downloadAndUpload({
      url: imageUrl,
      key: `downloaded_images/${targetFilename}`,
      // contentType can often be inferred from the source URL's headers
      // disposition: "inline",
    });
    console.log("Download and upload successful:", result.url);
    return result;
  } catch (error) {
    console.error("Download and upload failed:", error);
  }
}
```

### Example Route

An example API route `app/routes/api.upload-image.tsx` demonstrates both `uploadFile` (with a hardcoded base64 image) and `downloadAndUpload` (with a placeholder image URL). You can test its functionality by sending a POST request to `/api/upload-image`.

### Cloudflare R2 Specifics (Recommended)

Cloudflare R2 is a popular S3-compatible storage solution, especially when using Cloudflare Workers. Here's how the environment variables typically map for R2:

-   `STORAGE_ENDPOINT`: Your R2 account ID specific S3 API endpoint. You can find this in your R2 dashboard (e.g., `https://<ACCOUNT_ID>.r2.cloudflarestorage.com`).
-   `STORAGE_BUCKET`: The name of your R2 bucket.
-   `STORAGE_ACCESS_KEY`: The Access Key ID for an R2 API token you create. Ensure the token has "Object Write" permissions for uploading.
-   `STORAGE_SECRET_KEY`: The Secret Access Key for the same R2 API token.
-   `STORAGE_REGION`: For R2, this is typically `"auto"`.
-   `STORAGE_DOMAIN` (Optional): If you've connected a custom domain to your R2 bucket, you can specify it here (e.g., `https://r2.yourdomain.com`). This allows you to serve files from your own domain instead of the default R2 public URL (if your bucket is publicly accessible) or presigned URLs.

Refer to the [Cloudflare R2 documentation](https://developers.cloudflare.com/r2/) for details on creating buckets, API tokens, and configuring custom domains.
