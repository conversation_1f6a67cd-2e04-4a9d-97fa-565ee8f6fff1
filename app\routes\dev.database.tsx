import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { users } from "~/lib/db/schema";

export async function loader({ context }: LoaderFunctionArgs) {
  try {
    // Check if database is available
    const db = context.db;

    if (!db) {
      return json({
        success: false,
        error: "DATABASE_URL not configured",
        message: "Database not configured. Please set the DATABASE_URL environment variable.",
        userCount: 0,
      });
    }

    // Attempt to query the users table (an error is expected if the table does not exist)
    let userCount = 0;
    let error = null;

    try {
      const result = await db.select().from(users);
      userCount = result.length;
    } catch (e) {
      error = e instanceof Error ? e.message : "Unknown error";
    }

    return json({
      success: true,
      userCount,
      error,
      message: error
        ? "Database connection successful, but tables may not be created yet. Please run database migration."
        : "Database connection and query successful!",
    });
  } catch (error) {
    return json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      message: "Database connection failed",
      userCount: 0,
    });
  }
}

export default function TestDb() {
  const data = useLoaderData<typeof loader>();

  return (
    <div className="flex h-screen items-center justify-center">
      <div className="max-w-md mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold mb-4 text-gray-800 dark:text-gray-100">
          Database Connection Test
        </h1>

        <div
          className={`p-4 rounded-lg mb-4 ${
            data.success
              ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
              : "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"
          }`}
        >
          <p className="font-semibold">{data.success ? "✅ Success" : "❌ Failed"}</p>
          <p className="mt-2">{data.message}</p>
        </div>

        {data.error && (
          <div className="p-4 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-lg mb-4">
            <p className="font-semibold">Error Details:</p>
            <p className="mt-1 text-sm font-mono">{data.error}</p>
          </div>
        )}

        {data.success && !data.error && "userCount" in data && (
          <div className="p-4 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-lg">
            <p>User Count: {data.userCount}</p>
          </div>
        )}

        <div className="mt-6 text-sm text-gray-600 dark:text-gray-400">
          <p>If you see table does not exist errors, please run the following command:</p>
          <code className="block mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded">
            npm run db:push
          </code>
        </div>
      </div>
    </div>
  );
}
