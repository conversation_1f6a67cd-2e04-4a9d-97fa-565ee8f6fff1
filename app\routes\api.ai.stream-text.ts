import type { ActionFunctionArgs } from "@remix-run/node";
import { streamText } from "ai";
import type { AIProvider } from "~/lib/ai/ai-providers";
import {
  AI_ERROR_MESSAGES,
  createAIModelSafely,
  getAIErrorMessage,
  logAIOperation,
  sanitizePromptForLogging,
  validateAIRequest,
} from "~/lib/ai/ai-utils";
import { respErr } from "~/lib/api/resp";
import {
  CreditsAmount,
  CreditsTransType,
  decreaseCredits,
  getUserUuid,
} from "~/services/user-management.server";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  const startTime = Date.now();
  let provider: AIProvider | undefined;
  let model: string | undefined;

  try {
    const db = context.db;
    if (!db) {
      return respErr("Database not available");
    }
    // Parse request body
    const body = await request.json();

    // Validate request parameters
    if (!validateAIRequest(body)) {
      return respErr(AI_ERROR_MESSAGES.INVALID_PARAMS);
    }

    const { prompt, provider: reqProvider, model: reqModel } = body;
    provider = reqProvider;
    model = reqModel;

    // Check user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Log the operation start
    console.log("Starting AI text streaming:", {
      provider,
      model,
      prompt: sanitizePromptForLogging(prompt),
      user_uuid,
      timestamp: new Date().toISOString(),
    });

    // Create AI model
    const textModel = createAIModelSafely(provider, model, context);

    // Stream text generation
    const result = await streamText({
      model: textModel,
      prompt: prompt,
      maxTokens: 4000, // Reasonable limit
      temperature: 0.7, // Balanced creativity
      onChunk: async (chunk) => {
        // Log chunk information for debugging (optional)
        // Removed process.env check for Cloudflare Workers compatibility
        console.log("Stream chunk:", {
          type: chunk.type,
          provider,
          model,
        });
      },
      onFinish: async (result) => {
        // Decrease user credits after successful generation
        try {
          await decreaseCredits(
            {
              user_uuid,
              trans_type: CreditsTransType.AITextGeneration,
              credits: CreditsAmount.AITextGenerationCost,
            },
            db
          );
        } catch (creditError) {
          console.error("Failed to decrease credits:", creditError);
          // Continue even if credit deduction fails
        }

        // Log successful operation
        logAIOperation("stream-text", provider!, model!, true, Date.now() - startTime);

        console.log("Stream finished:", {
          provider,
          model,
          text: sanitizePromptForLogging(result.text, 200),
          usage: result.usage,
          finishReason: result.finishReason,
        });
      },
      onError: async (error) => {
        // Log failed operation
        logAIOperation("stream-text", provider!, model!, false, Date.now() - startTime, error);

        console.error("Stream error:", {
          error: error.message || error,
          provider,
          model,
        });
      },
    });

    // Return the streaming response
    return result.toDataStreamResponse({
      sendReasoning: true, // Include reasoning if available
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    });
  } catch (error) {
    // Log failed operation
    if (provider && model) {
      logAIOperation("stream-text", provider, model, false, Date.now() - startTime, error);
    }

    console.error("AI text streaming failed:", {
      error: error.message || error,
      provider,
      model,
      stack: error.stack,
    });

    // Return user-friendly error message
    const errorMessage = getAIErrorMessage(error);
    return respErr(errorMessage);
  }
}
