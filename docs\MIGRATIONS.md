# Database Migrations Guide

This document provides guidance on managing database migrations for the Remix + Vercel + Neon starter project.

## Overview

The project uses Drizzle ORM for database schema management and migrations. We have custom migration scripts to handle complex schema changes safely.

## Migration Files

### Current Migrations

1. **0000_flat_justice.sql** - Initial database schema
2. **0001_add_missing_fields.sql** - Adds missing fields referenced in the codebase

### Migration Structure

```
drizzle/
├── 0000_flat_justice.sql           # Initial schema
├── 0001_add_missing_fields.sql     # Add missing fields
├── 0001_add_missing_fields_rollback.sql  # Rollback script
└── meta/                           # Drizzle metadata
```

## Running Migrations

### Development Environment

1. **Dry Run (Recommended First)**
   ```bash
   npm run db:migrate:add-fields:dry
   ```

2. **Apply Migration**
   ```bash
   npm run db:migrate:add-fields
   ```

3. **Rollback if Needed**
   ```bash
   npm run db:migrate:rollback-fields
   ```

### Production Environment

1. **Always test in staging first**
2. **Run dry run to verify SQL**
   ```bash
   npm run db:migrate:add-fields:dry
   ```

3. **Apply migration during maintenance window**
   ```bash
   npm run db:migrate:add-fields
   ```

4. **Verify application functionality**

## Custom Migration Script

The `scripts/migrate.ts` script provides additional safety features:

### Features

- **Dry Run Mode**: Preview changes without applying them
- **Verbose Output**: Detailed logging of operations
- **Connection Testing**: Verifies database connectivity
- **Error Handling**: Graceful error handling and rollback

### Usage Examples

```bash
# Run specific migration with dry run
tsx scripts/migrate.ts run 0001_add_missing_fields.sql --dry-run --verbose

# Add missing fields (production ready)
tsx scripts/migrate.ts add-missing-fields

# Rollback missing fields
tsx scripts/migrate.ts rollback-missing-fields
```

## Migration 0001: Add Missing Fields

This migration adds fields that were referenced in the codebase but missing from the database schema.

### Changes Made

#### Accounts Table
- `user_id` (uuid) - Foreign key to users table
- `provider` (varchar) - OAuth provider name
- `provider_user_id` (varchar) - Provider-specific user ID
- `access_token` (text) - OAuth access token
- `refresh_token` (text) - OAuth refresh token
- `expires_at` (timestamp) - Token expiration time

#### Orders Table
- `order_no` (varchar) - Unique order number
- `billing_provider` (varchar) - Payment provider
- `user_email` (varchar) - Customer email
- `user_uuid` (uuid) - Customer UUID

#### Credit Transactions Table
- `trans_no` (varchar) - Unique transaction number

#### Notifications Table
- `account_id` (uuid) - Foreign key to accounts table

#### Subscriptions Table
- `account_id` (uuid) - Foreign key to accounts table
- `stripe_subscription_id` (varchar) - Stripe subscription ID
- `stripe_customer_id` (varchar) - Stripe customer ID

#### Sessions Table
- `session_token` (varchar) - Session token
- `is_active` (boolean) - Session active status

### Indexes Created

- Performance indexes on frequently queried fields
- Unique constraints on order numbers and transaction numbers
- Foreign key indexes for better join performance

## Safety Guidelines

### Before Running Migrations

1. **Backup Database**: Always backup before major schema changes
2. **Test in Staging**: Run migrations in staging environment first
3. **Review SQL**: Use dry-run mode to review generated SQL
4. **Plan Rollback**: Ensure rollback scripts are ready

### During Migration

1. **Maintenance Mode**: Put application in maintenance mode if needed
2. **Monitor Performance**: Watch for long-running queries
3. **Check Logs**: Monitor application and database logs
4. **Verify Data**: Spot-check data integrity after migration

### After Migration

1. **Test Application**: Verify all features work correctly
2. **Monitor Performance**: Watch for performance regressions
3. **Update Documentation**: Document any manual steps taken
4. **Clean Up**: Remove old migration files if no longer needed

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check DATABASE_URL environment variable
   - Verify network connectivity to Neon database
   - Increase connection timeout in migration script

2. **Permission Errors**
   - Ensure database user has necessary permissions
   - Check if tables are locked by other processes

3. **Constraint Violations**
   - Review existing data for conflicts
   - Consider data cleanup before migration
   - Use conditional constraints where appropriate

### Recovery Steps

1. **If Migration Fails Midway**
   ```bash
   # Check what was applied
   \d+ table_name  # In psql

   # Run rollback script
   npm run db:migrate:rollback-fields
   ```

2. **If Application Breaks**
   - Revert to previous deployment
   - Run rollback migration
   - Investigate and fix issues
   - Re-run migration when ready

## Environment Variables

Ensure these environment variables are set:

```bash
DATABASE_URL="postgresql://user:pass@host:port/db"
NODE_ENV="production|development"
```

## Best Practices

1. **Version Control**: Always commit migration files
2. **Naming Convention**: Use descriptive migration names
3. **Atomic Changes**: Keep migrations focused and atomic
4. **Documentation**: Document complex migrations
5. **Testing**: Test migrations thoroughly before production
6. **Monitoring**: Monitor application after migrations

## Support

For migration issues:

1. Check application logs
2. Review database logs in Neon console
3. Use dry-run mode to debug SQL
4. Consult this documentation
5. Contact development team if needed
