# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Package Manager**: This project uses **pnpm** as the package manager.

**Primary development workflow:**
- `pnpm run dev` - Start Remix dev server
- `pnpm run build` - Build for production
- `pnpm run start` - Start production server locally (after build)
- `pnpm run deploy` - Deploy to Vercel

**Code quality:**
- `pnpm run check` - Run all Biome checks (lint + format)
- `pnpm run check:fix` - Fix all auto-fixable issues
- `pnpm run lint` / `pnpm run lint:fix` - Biome linting
- `pnpm run format` / `pnpm run format:fix` - Biome formatting
- `pnpm run typecheck` - TypeScript type checking
- `pnpm run test` - Run Vitest tests

**Database (Drizzle + Neon):**
- `pnpm run db:generate` - Generate migration files
- `pnpm run db:push` - Push schema changes (development)
- `pnpm run db:migrate` - Run migrations
- `pnpm run db:studio` - Open Drizzle Studio GUI

**Environment setup:**
- Copy `.env.example` to `.env` for local environment variables
- DATABASE_URL required for Neon PostgreSQL connection
- API keys (OpenAI, Stripe, etc.) go in `.env` for local, Vercel dashboard for production

## Architecture Overview

**Tech Stack:**
- **Runtime**: Remix on Vercel
- **Database**: Neon PostgreSQL with Drizzle ORM
- **Storage**: Vercel Blob Storage
- **State**: Zustand stores with TypeScript
- **Styling**: Tailwind CSS + Radix UI components
- **Code Quality**: Biome (replaces ESLint + Prettier)
- **Analytics**: Google Analytics 4 integration
- **AI**: Multi-provider support (OpenAI, DeepSeek, OpenRouter, Replicate)
- **Payments**: Stripe integration
- **Testing**: Vitest with happy-dom environment

**Key Architectural Patterns:**

1. **Database**: 
   - Use `createDb(databaseUrl)` or `createDbFromEnv()` factory pattern
   - Modular schema structure in `app/lib/db/schemas/` (users, billing, chat, content, monitoring)
   - Enhanced database instance with health checks and configuration methods

2. **State Management**: Zustand stores with TypeScript selectors:
   - `useAuthStore` (aliased as `useUserStore`) - Authentication/user data
   - `useUIStore` - Theme, language, notifications, sidebar state
   - `useCartStore` - Shopping cart functionality
   - `useAppStore` - Global app state, initialization, online status

3. **AI Integration**:
   - Multi-provider support via `app/lib/ai/ai-providers.ts`
   - Use `createAIModel(providerId, modelId, env)` for model creation
   - Providers: OpenAI, DeepSeek, OpenRouter, Replicate
   - Registry pattern for model validation and capabilities

4. **Component Architecture**: 
   - UI components in `app/components/ui/` (shadcn-style with Radix UI)
   - Layout components in `app/components/layout/` (header, footer, sidebar)
   - Reusable blocks in `app/components/blocks/` for landing pages
   - Admin components in `app/components/admin/` with data tables

5. **Route Organization**: 
   - API routes prefixed with `api.` in `app/routes/`
   - Protected admin routes with `admin.` prefix
   - Console/dashboard routes with `console.` prefix
   - Authentication routes with `auth.` prefix
   - Test routes with `test.` prefix for development

6. **Configuration System**:
   - Centralized config exports in `app/config/index.ts`
   - Design system with theme, typography, and component variants
   - Environment-aware configuration with feature flags

**Authentication & Security:**
- JWT-based session management with refresh tokens
- Google OAuth integration with one-tap login
- Protected routes with middleware-based authentication
- Role-based access control for admin features

**Testing:**
- Vitest with happy-dom environment
- Test setup in `test/setup-test-env.ts`
- Component testing with Testing Library
- System tests for analytics, API keys, usage tracking

**Environment Variables:**
- Local: `.env` file (copy from `.env.example`)  
- Production: Vercel dashboard environment variables
- Required: DATABASE_URL, STRIPE keys
- Optional: AI provider keys (OPENAI_API_KEY, etc.), GA_TRACKING_ID