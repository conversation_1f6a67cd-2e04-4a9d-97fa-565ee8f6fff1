/**
 * API Key Management Service
 * Handles API key generation, validation, and management
 */

import crypto from "crypto";
import { and, count, desc, eq } from "drizzle-orm";
import type { Database } from "~/lib/db/db";
import type { ApiKey } from "~/lib/db/schema";
import { apiKeys, users } from "~/lib/db/schema";

export interface CreateApiKeyParams {
  title: string;
  userUuid: string;
  accountId?: string;
  expiresAt?: Date;
}

export interface ApiKeyWithUsage extends ApiKey {
  usageCount?: number;
  lastUsedFormatted?: string;
}

export interface ApiKeyValidationResult {
  isValid: boolean;
  apiKey?: ApiKey;
  error?: string;
}

/**
 * Generate a secure API key
 */
export function generateApiKey(): string {
  // Generate a secure random API key with prefix
  const randomBytes = crypto.randomBytes(32);
  const apiKey = `sk-${randomBytes.toString("hex")}`;
  return apiKey;
}

/**
 * Create a new API key
 */
export async function createApiKey(
  params: CreateApiKeyParams,
  db: Database
): Promise<{ success: boolean; apiKey?: ApiKey; error?: string }> {
  try {
    const { title, userUuid, accountId, expiresAt } = params;

    // Validate title
    if (!title || title.trim().length < 3) {
      return { success: false, error: "Title must be at least 3 characters long" };
    }

    if (title.length > 100) {
      return { success: false, error: "Title must be less than 100 characters" };
    }

    // Check if user exists
    const user = await db.select().from(users).where(eq(users.uuid, userUuid)).limit(1);

    if (!user || user.length === 0) {
      return { success: false, error: "User not found" };
    }

    // Check existing API key count (limit to 10 per user)
    const existingKeysCount = await db
      .select({ count: count() })
      .from(apiKeys)
      .where(and(eq(apiKeys.userUuid, userUuid), eq(apiKeys.status, "active")));

    if (existingKeysCount[0]?.count >= 10) {
      return {
        success: false,
        error: "Maximum number of API keys reached (10). Please delete some existing keys first.",
      };
    }

    // Generate unique API key
    let newApiKey: string;
    let attempts = 0;
    const maxAttempts = 5;

    do {
      newApiKey = generateApiKey();
      attempts++;

      // Check if key already exists
      const existingKey = await db
        .select()
        .from(apiKeys)
        .where(eq(apiKeys.apiKey, newApiKey))
        .limit(1);

      if (existingKey.length === 0) {
        break;
      }

      if (attempts >= maxAttempts) {
        return { success: false, error: "Failed to generate unique API key" };
      }
    } while (attempts < maxAttempts);

    // Create API key record
    const result = await db
      .insert(apiKeys)
      .values({
        apiKey: newApiKey,
        title: title.trim(),
        userUuid,
        accountId,
        status: "active",
        expiresAt,
      })
      .returning();

    if (result.length === 0) {
      return { success: false, error: "Failed to create API key" };
    }

    return { success: true, apiKey: result[0] };
  } catch (error) {
    console.error("Error creating API key:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create API key",
    };
  }
}

/**
 * Get user's API keys with pagination
 */
export async function getUserApiKeys(
  userUuid: string,
  options: {
    page?: number;
    limit?: number;
    includeRevoked?: boolean;
  } = {},
  db: Database
): Promise<{
  apiKeys: ApiKeyWithUsage[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}> {
  try {
    const { page = 1, limit = 20, includeRevoked = false } = options;
    const offset = (page - 1) * limit;

    // Build where conditions
    let whereConditions = eq(apiKeys.userUuid, userUuid);

    if (!includeRevoked) {
      whereConditions = and(whereConditions, eq(apiKeys.status, "active"));
    }

    // Get total count
    const totalCountResult = await db
      .select({ count: count() })
      .from(apiKeys)
      .where(whereConditions);

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    // Get API keys
    const apiKeyResults = await db
      .select()
      .from(apiKeys)
      .where(whereConditions)
      .orderBy(desc(apiKeys.createdAt))
      .limit(limit)
      .offset(offset);

    // Format API keys with additional info
    const formattedApiKeys: ApiKeyWithUsage[] = apiKeyResults.map((key) => ({
      ...key,
      lastUsedFormatted: key.lastUsedAt ? formatRelativeTime(key.lastUsedAt) : "Never",
      // TODO: Add usage count from usage tracking system
      usageCount: 0,
    }));

    return {
      apiKeys: formattedApiKeys,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  } catch (error) {
    console.error("Error getting user API keys:", error);
    return {
      apiKeys: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        hasNext: false,
        hasPrev: false,
      },
    };
  }
}

/**
 * Validate API key and return key info
 */
export async function validateApiKey(
  apiKeyString: string,
  db: Database
): Promise<ApiKeyValidationResult> {
  try {
    if (!apiKeyString || !apiKeyString.startsWith("sk-")) {
      return { isValid: false, error: "Invalid API key format" };
    }

    const apiKey = await db.select().from(apiKeys).where(eq(apiKeys.apiKey, apiKeyString)).limit(1);

    if (!apiKey || apiKey.length === 0) {
      return { isValid: false, error: "API key not found" };
    }

    const key = apiKey[0];

    // Check if key is active
    if (key.status !== "active") {
      return { isValid: false, error: "API key is not active" };
    }

    // Check if key is expired
    if (key.expiresAt && new Date() > key.expiresAt) {
      return { isValid: false, error: "API key has expired" };
    }

    return { isValid: true, apiKey: key };
  } catch (error) {
    console.error("Error validating API key:", error);
    return { isValid: false, error: "Failed to validate API key" };
  }
}

/**
 * Update API key last used timestamp
 */
export async function updateApiKeyLastUsed(apiKeyString: string, db: Database): Promise<void> {
  try {
    await db
      .update(apiKeys)
      .set({ lastUsedAt: new Date() })
      .where(eq(apiKeys.apiKey, apiKeyString));
  } catch (error) {
    console.error("Error updating API key last used:", error);
  }
}

/**
 * Revoke API key
 */
export async function revokeApiKey(
  apiKeyId: number,
  userUuid: string,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    const result = await db
      .update(apiKeys)
      .set({ status: "revoked" })
      .where(and(eq(apiKeys.id, apiKeyId), eq(apiKeys.userUuid, userUuid)))
      .returning();

    if (result.length === 0) {
      return { success: false, error: "API key not found or access denied" };
    }

    return { success: true };
  } catch (error) {
    console.error("Error revoking API key:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to revoke API key",
    };
  }
}

/**
 * Delete API key permanently
 */
export async function deleteApiKey(
  apiKeyId: number,
  userUuid: string,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    const result = await db
      .delete(apiKeys)
      .where(and(eq(apiKeys.id, apiKeyId), eq(apiKeys.userUuid, userUuid)))
      .returning();

    if (result.length === 0) {
      return { success: false, error: "API key not found or access denied" };
    }

    return { success: true };
  } catch (error) {
    console.error("Error deleting API key:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete API key",
    };
  }
}

/**
 * Update API key title
 */
export async function updateApiKeyTitle(
  apiKeyId: number,
  userUuid: string,
  newTitle: string,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    if (!newTitle || newTitle.trim().length < 3) {
      return { success: false, error: "Title must be at least 3 characters long" };
    }

    if (newTitle.length > 100) {
      return { success: false, error: "Title must be less than 100 characters" };
    }

    const result = await db
      .update(apiKeys)
      .set({ title: newTitle.trim() })
      .where(and(eq(apiKeys.id, apiKeyId), eq(apiKeys.userUuid, userUuid)))
      .returning();

    if (result.length === 0) {
      return { success: false, error: "API key not found or access denied" };
    }

    return { success: true };
  } catch (error) {
    console.error("Error updating API key title:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update API key title",
    };
  }
}

/**
 * Get API key by ID (for user)
 */
export async function getApiKeyById(
  apiKeyId: number,
  userUuid: string,
  db: Database
): Promise<ApiKey | null> {
  try {
    const result = await db
      .select()
      .from(apiKeys)
      .where(and(eq(apiKeys.id, apiKeyId), eq(apiKeys.userUuid, userUuid)))
      .limit(1);

    return result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error("Error getting API key by ID:", error);
    return null;
  }
}

/**
 * Format relative time
 */
function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return "Just now";
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  if (days < 30) return `${Math.floor(days / 7)}w ago`;
  if (days < 365) return `${Math.floor(days / 30)}mo ago`;
  return `${Math.floor(days / 365)}y ago`;
}
