/**
 * 极简认证状态管理 - Zustand Store
 * 只管理 UI 需要的用户状态
 */

import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import type { AuthUser } from "~/lib/auth/jwt.server";

// 扩展用户接口，添加 UI 需要的字段
export interface User extends AuthUser {
  createdAt?: Date;
  sessionId?: string;
}

// Authentication state interface
interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  login: (user: User) => void;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
  updateCredits: (credits: number) => void;
  clearError: () => void;

  // Async actions
  checkAuth: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

// Create the auth store
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Sync actions
      setUser: (user) => {
        set({
          user,
          isAuthenticated: !!user,
          error: null,
        });
      },

      setLoading: (isLoading) => {
        set({ isLoading });
      },

      setError: (error) => {
        set({ error });
      },

      login: (user) => {
        set({
          user,
          isAuthenticated: true,
          error: null,
        });
      },

      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          error: null,
        });
      },

      updateUser: (updates) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...updates },
          });
        }
      },

      updateCredits: (credits) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, credits },
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // 简化的认证检查
      checkAuth: async () => {
        const { setLoading, setUser, setError } = get();

        setLoading(true);
        setError(null);

        try {
          const response = await fetch("/api/auth/me", {
            method: "GET",
            credentials: "include",
          });

          if (response.ok) {
            const data = await response.json();
            if (data.success && data.user) {
              setUser(data.user);
            } else {
              setUser(null);
            }
          } else {
            setUser(null);
          }
        } catch (error) {
          console.error("Auth check failed:", error);
          setUser(null);
        } finally {
          setLoading(false);
        }
      },

      // 简化的刷新认证（实际上在新架构中不需要）
      refreshAuth: async () => {
        // 在新的 JWT 架构中，刷新由服务端自动处理
        // 这里只是为了保持接口兼容性
        await get().checkAuth();
      },
    }),
    {
      name: "auth-storage",
      storage: createJSONStorage(() => {
        if (typeof window !== "undefined") {
          return localStorage;
        }
        // Return a no-op storage for server-side
        return {
          getItem: () => null,
          setItem: () => {},
          removeItem: () => {},
        };
      }),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Selectors for easier access
export const useUser = () => useAuthStore((state) => state.user);
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated);
export const useAuthLoading = () => useAuthStore((state) => state.isLoading);
export const useAuthError = () => useAuthStore((state) => state.error);

// Auth actions
export const useAuthActions = () => {
  const store = useAuthStore();
  return {
    login: store.login,
    logout: store.logout,
    updateUser: store.updateUser,
    updateCredits: store.updateCredits,
    setError: store.setError,
    clearError: store.clearError,
    checkAuth: store.checkAuth,
    refreshAuth: store.refreshAuth,
  };
};
