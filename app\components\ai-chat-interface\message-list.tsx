import { Loader2, MessageSquare } from "lucide-react";
import { memo, useEffect, useRef } from "react";
import { cn } from "~/lib/utils/utils";
import MessageItem from "./message-item";
import type { Message } from "./types";

interface MessageListProps {
  messages: Message[];
  isLoading?: boolean;
  isStreaming?: boolean;
  className?: string;
}

export const MessageList = memo(function MessageList({
  messages,
  isLoading = false,
  isStreaming = false,
  className,
}: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    }
  }, [messages.length, isStreaming]);

  // Empty state
  if (messages.length === 0 && !isLoading) {
    return (
      <div className={cn("flex-1 flex items-center justify-center", className)}>
        <div className="text-center text-muted-foreground max-w-md">
          <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-40" />
          <h3 className="text-lg font-medium mb-2">Start a conversation</h3>
          <p className="text-sm leading-6">
            Ask me anything! I can help with coding, writing, analysis, and much more.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={scrollContainerRef}
      className={cn("flex-1 overflow-y-auto", className)}
      role="log"
      aria-label="Chat messages"
      aria-live="polite"
    >
      <div className="min-h-full">
        {messages.map((message, index) => (
          <MessageItem
            key={message.id}
            message={message}
            isLast={index === messages.length - 1 && !isLoading && !isStreaming}
          />
        ))}

        {/* Loading indicator */}
        {(isLoading || isStreaming) && (
          <div className="flex gap-4 p-4">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-muted border border-border rounded-lg flex items-center justify-center">
                <Loader2 className="h-4 w-4 animate-spin" />
              </div>
            </div>
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <span className="font-medium text-foreground">Assistant</span>
              </div>
              <div className="text-sm text-muted-foreground">
                {isStreaming ? "Thinking..." : "Processing your request..."}
              </div>
            </div>
          </div>
        )}

        {/* Scroll anchor */}
        <div ref={messagesEndRef} className="h-1" />
      </div>
    </div>
  );
});

export default MessageList;
