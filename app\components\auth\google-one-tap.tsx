/**
 * Google One Tap Authentication Component
 * Auto popup + manual fallback button
 */

import { useEffect, useState } from "react";

// Google One Tap types
interface GoogleOneTapConfig {
  client_id: string;
  callback: (response: { credential: string }) => void;
  auto_select?: boolean;
  cancel_on_tap_outside?: boolean;
}

interface GoogleOneTapProps {
  clientId?: string;
  enabled?: boolean;
}

// Extend window type for Google APIs
interface GoogleCredentialResponse {
  credential: string;
  select_by?: string;
}

interface GoogleNotification {
  isNotDisplayed?: () => boolean;
  getNotDisplayedReason?: () => string;
}

declare global {
  interface Window {
    google?: {
      accounts?: {
        id?: {
          initialize: (config: {
            client_id: string;
            callback: (response: GoogleCredentialResponse) => void;
          }) => void;
          prompt: (callback?: (notification: GoogleNotification) => void) => void;
        };
      };
    };
  }
}

export function GoogleOneTap({ clientId, enabled = true }: GoogleOneTapProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!enabled || !clientId) return;

    // Load Google One Tap script
    const script = document.createElement("script");
    script.src = "https://accounts.google.com/gsi/client";
    script.async = true;
    script.defer = true;

    script.onload = () => {
      try {
        // Initialize Google One Tap
        if (window.google?.accounts?.id) {
          window.google.accounts.id.initialize({
            client_id: clientId,
            callback: (response: GoogleCredentialResponse) => {
              // Set cookie and redirect to auth handler
              if (response.credential) {
                // Using traditional cookie setting (fallback for older browsers)
                // biome-ignore lint/suspicious/noDocumentCookie: Google Auth requires direct cookie access
                document.cookie = `g_credential=${response.credential}; Path=/; SameSite=Lax${window.location.protocol === "https:" ? "; Secure" : ""}`;
                // Redirect to auth handler instead of reload
                window.location.href = "/auth/google/callback";
              }
            },
            auto_select: false,
            cancel_on_tap_outside: true,
            use_fedcm_for_prompt: false, // Disable FedCM for better localhost compatibility
          });

          // Show One Tap prompt
          window.google.accounts.id.prompt((notification: GoogleNotification) => {
            if (notification.isNotDisplayed?.()) {
              console.log("One Tap not displayed:", notification.getNotDisplayedReason());
            }
          });

          setIsLoaded(true);
        }
      } catch (err) {
        console.error("Google One Tap initialization error:", err);
        setError("Failed to initialize Google authentication");
      }
    };

    script.onerror = () => {
      setError("Failed to load Google authentication");
    };

    document.head.appendChild(script);

    return () => {
      const existingScript = document.querySelector(
        'script[src="https://accounts.google.com/gsi/client"]'
      );
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, [clientId, enabled]);

  // Show manual Google Sign-In button if One Tap fails or as fallback
  if (!clientId) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-sm text-yellow-800">
          Google authentication is not configured. Please check your environment variables.
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-sm text-red-800">{error}</p>
      </div>
    );
  }

  // Render manual Google Sign-In button as fallback
  return (
    <div className="space-y-4">
      <GoogleSignInButton clientId={clientId} />
      {isLoaded && (
        <p className="text-xs text-gray-500 text-center">
          Google One Tap should appear automatically, or use the button above
        </p>
      )}
    </div>
  );
}

/**
 * Manual Google Sign-In Button Component
 */
export function GoogleSignInButton({ clientId }: { clientId?: string }) {
  const handleGoogleSignIn = () => {
    if (!clientId || !window.google || !window.google.accounts || !window.google.accounts.id) {
      console.error("Google Sign-In not available");
      return;
    }

    // Trigger Google sign-in popup
    window.google.accounts.id.prompt();
  };

  return (
    <button
      type="button"
      onClick={handleGoogleSignIn}
      className="w-full flex items-center justify-center gap-3 px-6 py-3 border border-gray-300 rounded-lg shadow-sm bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 font-medium"
    >
      <svg className="w-5 h-5" viewBox="0 0 24 24">
        <path
          fill="#4285F4"
          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
        />
        <path
          fill="#34A853"
          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
        />
        <path
          fill="#FBBC05"
          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
        />
        <path
          fill="#EA4335"
          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
        />
      </svg>
      Continue with Google
    </button>
  );
}

/**
 * Neon Auth Fallback Button Component
 * Shows when Google One Tap is not available
 */
export function NeonFallbackButton() {
  const handleNeonAuth = () => {
    // For now, just show an alert - you can integrate Neon Auth here
    alert("Neon Auth integration coming soon! Please use Google Sign-In for now.");
    // TODO: Integrate with Neon Auth
    // Example: window.location.href = "/neon-auth/sign-in";
  };

  return (
    <div className="space-y-4">
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">Or continue with email</span>
        </div>
      </div>

      <button
        type="button"
        onClick={handleNeonAuth}
        className="w-full flex items-center justify-center gap-3 px-6 py-3 border border-gray-300 rounded-lg shadow-sm bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 font-medium"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
          />
        </svg>
        Continue with Email
      </button>
    </div>
  );
}
