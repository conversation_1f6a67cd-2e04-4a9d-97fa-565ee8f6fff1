import { Link } from "@remix-run/react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import HeroBg from "./bg";
import HappyUsers from "./happy-users";

export interface HeroButton {
  title: string;
  url: string;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  target?: string;
  icon?: string;
}

export interface HeroAnnouncement {
  title: string;
  url: string;
  label?: string;
}

export interface HeroProps {
  title: string;
  description?: string;
  highlight_text?: string;
  buttons?: HeroButton[];
  announcement?: HeroAnnouncement;
  tip?: string;
  show_badge?: boolean;
  show_happy_users?: boolean;
  disabled?: boolean;
}

export default function Hero({
  title,
  description,
  highlight_text,
  buttons,
  announcement,
  tip,
  show_badge = false,
  show_happy_users = false,
  disabled = false,
}: HeroProps) {
  if (disabled) {
    return null;
  }

  // Split title by highlight text for gradient effect
  let texts = null;
  if (highlight_text) {
    texts = title?.split(highlight_text, 2);
  }

  return (
    <>
      <HeroBg />
      <section className="py-40 relative overflow-hidden">
        {/* Minimal visual elements */}
        <div className="absolute inset-0 pointer-events-none opacity-30">
          <div className="absolute top-20 left-1/4 w-1 h-16 bg-border" />
          <div className="absolute bottom-20 right-1/4 w-1 h-16 bg-border" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {show_badge && (
            <div className="flex items-center justify-center mb-12">
              <div className="px-6 py-3 bg-card border border-border rounded-lg shadow-sm">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-success rounded-full" />
                  <span className="text-sm font-medium text-foreground">
                    🚀 AI-Powered SaaS Platform - Now Live!
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="text-center space-y-8">
            {announcement && (
              <Link
                to={announcement.url}
                className="mx-auto mb-6 inline-flex items-center gap-3 rounded-lg border border-border bg-card px-4 py-2 text-sm hover:bg-accent transition-all duration-200 shadow-sm hover:shadow-md"
              >
                {announcement.label && (
                  <Badge
                    variant="secondary"
                    className="bg-primary text-primary-foreground border-0"
                  >
                    {announcement.label}
                  </Badge>
                )}
                <span className="font-medium">{announcement.title}</span>
                <svg
                  className="w-4 h-4 transition-transform group-hover:translate-x-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Link>
            )}

            {texts && texts.length > 1 ? (
              <h1 className="mx-auto mb-8 mt-6 max-w-5xl text-balance text-4xl font-bold lg:mb-12 lg:text-6xl leading-tight tracking-tight">
                <span className="block text-foreground">{texts[0]}</span>
                <span className="block text-primary">{highlight_text}</span>
                <span className="block text-foreground">{texts[1]}</span>
              </h1>
            ) : (
              <h1 className="mx-auto mb-8 mt-6 max-w-5xl text-balance text-4xl font-bold lg:mb-12 lg:text-6xl leading-tight tracking-tight text-foreground">
                {title}
              </h1>
            )}

            {description && (
              <p className="mx-auto max-w-3xl text-lg text-muted-foreground lg:text-xl leading-relaxed">
                {description}
              </p>
            )}

            {buttons && buttons.length > 0 && (
              <div className="mt-12 flex flex-col justify-center gap-4 sm:flex-row">
                {buttons.map((button, i) => (
                  <Link
                    key={`${button.text || button.label}-${i}`}
                    to={button.url}
                    target={button.target || ""}
                  >
                    <Button
                      className={`
                        px-8 py-3 text-base font-medium transition-all duration-200 rounded-lg
                        ${
                          i === 0
                            ? "bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm hover:shadow-md"
                            : "border border-border bg-background hover:bg-accent shadow-sm hover:shadow-md"
                        }
                      `}
                      size="lg"
                      variant={button.variant || "default"}
                    >
                      <div className="flex items-center gap-2">
                        {button.icon && <span className="text-lg">{button.icon}</span>}
                        <span>{button.title}</span>
                      </div>
                    </Button>
                  </Link>
                ))}
              </div>
            )}

            {tip && (
              <p className="mt-8 text-sm text-muted-foreground flex items-center justify-center gap-2">
                <svg
                  className="w-4 h-4 text-success"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                {tip}
              </p>
            )}

            {show_happy_users && <HappyUsers />}
          </div>
        </div>
      </section>
    </>
  );
}
