/**
 * 迁移脚本：添加 Google 认证所需的数据库字段
 */

import { drizzle } from "drizzle-orm/neon-http";
import { neon } from "@neondatabase/serverless";
import { existsSync, readFileSync } from "fs";
import { join } from "path";

// 加载环境变量
function loadEnvironmentVariables(): Record<string, string> {
  const envVars: Record<string, string> = {};

  // 尝试从 .env 文件加载
  const envPath = join(process.cwd(), ".env");
  if (existsSync(envPath)) {
    try {
      const envContent = readFileSync(envPath, "utf-8");
      const vars = envContent.split("\n").reduce(
        (acc, line) => {
          const trimmedLine = line.trim();
          if (trimmedLine && !trimmedLine.startsWith("#")) {
            const [key, ...valueParts] = trimmedLine.split("=");
            if (key && valueParts.length > 0) {
              let value = valueParts.join("=").trim();
              if (
                (value.startsWith('"') && value.endsWith('"')) ||
                (value.startsWith("'") && value.endsWith("'"))
              ) {
                value = value.slice(1, -1);
              }
              acc[key.trim()] = value;
            }
          }
          return acc;
        },
        {} as Record<string, string>
      );

      Object.assign(envVars, vars);
      console.log("✓ 已加载环境变量");
    } catch (error) {
      console.warn("⚠️  无法读取 .env 文件:", error);
    }
  }

  return envVars;
}

// 加载环境变量
const envVars = loadEnvironmentVariables();
Object.assign(process.env, envVars);

// 从环境变量获取数据库连接
const databaseUrl = process.env.DATABASE_URL;
if (!databaseUrl) {
  throw new Error("DATABASE_URL environment variable is required");
}

const sql = neon(databaseUrl);
const db = drizzle(sql);

async function migrateGoogleAuthFields() {
  console.log("🚀 开始迁移 Google 认证字段...");

  try {
    // 添加 users 表的新字段
    console.log("📝 添加 users 表字段...");
    
    await sql`
      ALTER TABLE users 
      ADD COLUMN IF NOT EXISTS uuid UUID DEFAULT gen_random_uuid(),
      ADD COLUMN IF NOT EXISTS theme VARCHAR(20) DEFAULT 'system',
      ADD COLUMN IF NOT EXISTS signin_type VARCHAR(50),
      ADD COLUMN IF NOT EXISTS signin_provider VARCHAR(50),
      ADD COLUMN IF NOT EXISTS signin_openid VARCHAR(255),
      ADD COLUMN IF NOT EXISTS signin_ip VARCHAR(45),
      ADD COLUMN IF NOT EXISTS is_affiliate BOOLEAN DEFAULT false NOT NULL;
    `;

    // 添加 sessions 表的新字段
    console.log("📝 添加 sessions 表字段...");
    
    await sql`
      ALTER TABLE sessions
      ADD COLUMN IF NOT EXISTS session_token VARCHAR(255),
      ADD COLUMN IF NOT EXISTS refresh_token VARCHAR(255),
      ADD COLUMN IF NOT EXISTS refresh_expires_at TIMESTAMP,
      ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true NOT NULL;
    `;

    // 创建索引
    console.log("📝 创建索引...");
    
    await sql`
      CREATE INDEX IF NOT EXISTS users_uuid_idx ON users(uuid);
    `;
    
    await sql`
      CREATE INDEX IF NOT EXISTS users_signin_provider_idx ON users(signin_provider);
    `;
    
    await sql`
      CREATE INDEX IF NOT EXISTS users_signin_openid_idx ON users(signin_openid);
    `;
    
    await sql`
      CREATE INDEX IF NOT EXISTS sessions_token_idx ON sessions(session_token);
    `;
    
    await sql`
      CREATE INDEX IF NOT EXISTS sessions_refresh_token_idx ON sessions(refresh_token);
    `;
    
    await sql`
      CREATE INDEX IF NOT EXISTS sessions_active_idx ON sessions(is_active);
    `;

    // 更新现有数据
    console.log("📝 更新现有数据...");
    
    // 为现有用户生成 UUID
    await sql`
      UPDATE users 
      SET uuid = gen_random_uuid() 
      WHERE uuid IS NULL;
    `;

    // 为现有会话生成 token（使用 md5 和 random() 生成随机字符串）
    await sql`
      UPDATE sessions
      SET
        session_token = COALESCE(session_token, md5(random()::text || clock_timestamp()::text)),
        refresh_token = COALESCE(refresh_token, md5(random()::text || clock_timestamp()::text || 'refresh')),
        refresh_expires_at = COALESCE(refresh_expires_at, expires_at + INTERVAL '23 days'),
        is_active = COALESCE(is_active, true)
      WHERE session_token IS NULL OR refresh_token IS NULL;
    `;

    // 添加唯一约束（先检查是否存在）
    console.log("📝 添加约束...");

    try {
      await sql`
        ALTER TABLE users
        ADD CONSTRAINT users_uuid_unique UNIQUE (uuid);
      `;
    } catch (error: any) {
      if (!error.message?.includes('already exists')) {
        throw error;
      }
      console.log("  - users_uuid_unique 约束已存在");
    }

    try {
      await sql`
        ALTER TABLE sessions
        ADD CONSTRAINT sessions_session_token_unique UNIQUE (session_token);
      `;
    } catch (error: any) {
      if (!error.message?.includes('already exists')) {
        throw error;
      }
      console.log("  - sessions_session_token_unique 约束已存在");
    }

    try {
      await sql`
        ALTER TABLE sessions
        ADD CONSTRAINT sessions_refresh_token_unique UNIQUE (refresh_token);
      `;
    } catch (error: any) {
      if (!error.message?.includes('already exists')) {
        throw error;
      }
      console.log("  - sessions_refresh_token_unique 约束已存在");
    }

    // 设置 NOT NULL 约束
    console.log("📝 设置 NOT NULL 约束...");

    await sql`
      ALTER TABLE users
      ALTER COLUMN uuid SET NOT NULL;
    `;

    await sql`
      ALTER TABLE sessions
      ALTER COLUMN session_token SET NOT NULL;
    `;

    await sql`
      ALTER TABLE sessions
      ALTER COLUMN refresh_token SET NOT NULL;
    `;

    await sql`
      ALTER TABLE sessions
      ALTER COLUMN refresh_expires_at SET NOT NULL;
    `;

    console.log("✅ Google 认证字段迁移完成！");
    
  } catch (error) {
    console.error("❌ 迁移失败:", error);
    throw error;
  }
}

// 运行迁移
migrateGoogleAuthFields()
  .then(() => {
    console.log("🎉 迁移成功完成！");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 迁移失败:", error);
    process.exit(1);
  });

export { migrateGoogleAuthFields };
