/**
 * Notification Service
 * Handles in-app notifications, email notifications, and notification preferences
 */

import { and, count, desc, eq } from "drizzle-orm";
import type { Database } from "~/lib/db/db";
import { notifications, users } from "~/lib/db/schema";
import { createEmailService, emailService } from "~/lib/email/service.server";
import { getTemplate } from "~/lib/email/templates";
import type { EnvironmentContext } from "~/lib/utils/env.server";

export type NotificationType =
  | "info"
  | "success"
  | "warning"
  | "error"
  | "payment"
  | "credit"
  | "usage"
  | "system"
  | "security";

export type NotificationChannel = "in_app" | "email" | "both";

export interface CreateNotificationParams {
  accountId: string;
  userId?: string;
  title: string;
  body: string;
  type: NotificationType;
  channel: NotificationChannel;
  link?: string;
  expiresAt?: Date;
  emailTemplate?: string;
  emailVariables?: Record<string, any>;
  env?: EnvironmentContext; // Add environment context for Cloudflare Workers
}

export interface NotificationPreferences {
  emailNotifications: boolean;
  pushNotifications: boolean;
  marketingEmails: boolean;
  usageAlerts: boolean;
  securityAlerts: boolean;
  paymentNotifications: boolean;
  creditNotifications: boolean;
  systemUpdates: boolean;
}

export interface UserNotification {
  id: number;
  title: string;
  body: string;
  type: NotificationType;
  channel: NotificationChannel;
  link?: string;
  dismissed: boolean;
  readAt?: Date;
  expiresAt?: Date;
  createdAt: Date;
}

/**
 * Create a new notification
 */
export async function createNotification(
  params: CreateNotificationParams,
  db: Database
): Promise<{ success: boolean; notificationId?: number; error?: string }> {
  try {
    const {
      accountId,
      userId,
      title,
      body,
      type,
      channel,
      link,
      expiresAt,
      emailTemplate,
      emailVariables,
      env,
    } = params;

    // Create in-app notification if channel includes in_app
    let notificationId: number | undefined;
    if (channel === "in_app" || channel === "both") {
      const result = await db
        .insert(notifications)
        .values({
          accountId,
          userId,
          title,
          body,
          type,
          channel: "in_app",
          link,
          expiresAt,
        })
        .returning({ id: notifications.id });

      notificationId = result[0]?.id;
    }

    // Send email notification if channel includes email
    if (channel === "email" || channel === "both") {
      await sendEmailNotification(
        {
          accountId,
          userId,
          title,
          body,
          type,
          emailTemplate,
          emailVariables,
          env,
        },
        db
      );
    }

    return { success: true, notificationId };
  } catch (error) {
    console.error("Error creating notification:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create notification",
    };
  }
}

/**
 * Send email notification
 */
async function sendEmailNotification(
  params: {
    accountId: string;
    userId?: string;
    title: string;
    body: string;
    type: NotificationType;
    emailTemplate?: string;
    emailVariables?: Record<string, any>;
    env?: EnvironmentContext;
  },
  db: Database
): Promise<void> {
  try {
    // Get user email
    const userQuery = params.userId
      ? eq(users.id, params.userId)
      : eq(users.uuid, params.accountId); // Assuming accountId maps to user UUID

    const user = await db
      .select({ email: users.email, name: users.name })
      .from(users)
      .where(userQuery)
      .limit(1);

    if (!user || user.length === 0) {
      console.error("User not found for email notification");
      return;
    }

    const { email, name } = user[0];

    // Use custom template if provided, otherwise use default notification template
    const templateName = params.emailTemplate || "notification";
    const template = getTemplate(templateName);

    if (!template) {
      console.error(`Email template not found: ${templateName}`);
      return;
    }

    const templateVariables = {
      name,
      title: params.title,
      body: params.body,
      type: params.type,
      ...params.emailVariables,
    };

    // Create email service with environment context
    const emailServiceWithEnv = createEmailService(params.env);

    const result = await emailServiceWithEnv.sendEmail({
      to: { email, name },
      from: { email: "<EMAIL>", name: "Your App" }, // TODO: Configure from env
      subject: template.subject(templateVariables),
      html: template.html(templateVariables),
      text: template.text(templateVariables),
      tags: [
        { name: "NotificationType", value: params.type },
        { name: "EmailType", value: "Notification" },
      ],
    });

    if (!result.success) {
      console.error("Failed to send email notification:", result.error);
    }
  } catch (error) {
    console.error("Error sending email notification:", error);
  }
}

/**
 * Get user notifications with pagination
 */
export async function getUserNotifications(
  accountId: string,
  options: {
    page?: number;
    limit?: number;
    unreadOnly?: boolean;
    type?: NotificationType;
  } = {},
  db: Database
): Promise<{
  notifications: UserNotification[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}> {
  try {
    const { page = 1, limit = 20, unreadOnly = false, type } = options;
    const offset = (page - 1) * limit;

    // Build where conditions
    let whereConditions = eq(notifications.accountId, accountId);

    if (unreadOnly) {
      whereConditions = and(whereConditions, eq(notifications.readAt, null));
    }

    if (type) {
      whereConditions = and(whereConditions, eq(notifications.type, type));
    }

    // Get total count
    const totalCountResult = await db
      .select({ count: count() })
      .from(notifications)
      .where(whereConditions);

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    // Get notifications
    const notificationResults = await db
      .select()
      .from(notifications)
      .where(whereConditions)
      .orderBy(desc(notifications.createdAt))
      .limit(limit)
      .offset(offset);

    const userNotifications: UserNotification[] = notificationResults.map((n) => ({
      id: n.id,
      title: n.title,
      body: n.body,
      type: n.type as NotificationType,
      channel: n.channel as NotificationChannel,
      link: n.link,
      dismissed: n.dismissed,
      readAt: n.readAt,
      expiresAt: n.expiresAt,
      createdAt: n.createdAt,
    }));

    return {
      notifications: userNotifications,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  } catch (error) {
    console.error("Error getting user notifications:", error);
    return {
      notifications: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        hasNext: false,
        hasPrev: false,
      },
    };
  }
}

/**
 * Mark notification as read
 */
export async function markNotificationAsRead(
  notificationId: number,
  accountId: string,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    await db
      .update(notifications)
      .set({ readAt: new Date() })
      .where(and(eq(notifications.id, notificationId), eq(notifications.accountId, accountId)));

    return { success: true };
  } catch (error) {
    console.error("Error marking notification as read:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to mark notification as read",
    };
  }
}

/**
 * Mark notification as dismissed
 */
export async function dismissNotification(
  notificationId: number,
  accountId: string,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    await db
      .update(notifications)
      .set({ dismissed: true })
      .where(and(eq(notifications.id, notificationId), eq(notifications.accountId, accountId)));

    return { success: true };
  } catch (error) {
    console.error("Error dismissing notification:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to dismiss notification",
    };
  }
}

/**
 * Mark all notifications as read
 */
export async function markAllNotificationsAsRead(
  accountId: string,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    await db
      .update(notifications)
      .set({ readAt: new Date() })
      .where(and(eq(notifications.accountId, accountId), eq(notifications.readAt, null)));

    return { success: true };
  } catch (error) {
    console.error("Error marking all notifications as read:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to mark all notifications as read",
    };
  }
}

/**
 * Get unread notification count
 */
export async function getUnreadNotificationCount(accountId: string, db: Database): Promise<number> {
  try {
    const result = await db
      .select({ count: count() })
      .from(notifications)
      .where(
        and(
          eq(notifications.accountId, accountId),
          eq(notifications.readAt, null),
          eq(notifications.dismissed, false)
        )
      );

    return result[0]?.count || 0;
  } catch (error) {
    console.error("Error getting unread notification count:", error);
    return 0;
  }
}

/**
 * Clean up expired notifications
 */
export async function cleanupExpiredNotifications(db: Database): Promise<void> {
  try {
    const now = new Date();
    await db
      .update(notifications)
      .set({ dismissed: true })
      .where(
        and(
          eq(notifications.dismissed, false)
          // notifications.expiresAt < now (need to implement proper date comparison)
        )
      );
  } catch (error) {
    console.error("Error cleaning up expired notifications:", error);
  }
}

// =============================================================================
// NOTIFICATION HELPER FUNCTIONS
// Convenient functions for creating common notification types
// =============================================================================

/**
 * Send payment success notification
 */
export async function notifyPaymentSuccess(
  userUuid: string,
  params: {
    amount: string;
    currency: string;
    planName: string;
    creditsAdded: number;
    nextBillingDate?: string;
    subscriptionUrl?: string;
    invoiceUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification(
      {
        accountId: userUuid,
        title: "Payment Successful",
        body: `Your payment of ${params.amount} ${params.currency.toUpperCase()} for ${params.planName} has been processed. ${params.creditsAdded} credits have been added to your account.`,
        type: "payment",
        channel: "both",
        link: params.subscriptionUrl || "/console/subscription",
        emailTemplate: "payment-success",
        emailVariables: params,
      },
      db
    );
  } catch (error) {
    console.error("Error sending payment success notification:", error);
  }
}

/**
 * Send payment failed notification
 */
export async function notifyPaymentFailed(
  userUuid: string,
  params: {
    amount: string;
    currency: string;
    planName: string;
    retryUrl?: string;
    supportUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification(
      {
        accountId: userUuid,
        title: "Payment Failed",
        body: `We couldn't process your payment of ${params.amount} ${params.currency.toUpperCase()} for ${params.planName}. Please update your payment method to continue your subscription.`,
        type: "error",
        channel: "both",
        link: params.retryUrl || "/console/subscription",
        emailTemplate: "notification",
        emailVariables: {
          actionUrl: params.retryUrl || "/console/subscription",
          actionText: "Update Payment Method",
          ...params,
        },
      },
      db
    );
  } catch (error) {
    console.error("Error sending payment failed notification:", error);
  }
}

/**
 * Send credits added notification
 */
export async function notifyCreditsAdded(
  userUuid: string,
  params: {
    creditsAdded: number;
    reason: string;
    newBalance: number;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification(
      {
        accountId: userUuid,
        title: "Credits Added",
        body: `${params.creditsAdded} credits have been added to your account. ${params.reason}. Your new balance is ${params.newBalance} credits.`,
        type: "credit",
        channel: "in_app",
        link: "/console/credits",
      },
      db
    );
  } catch (error) {
    console.error("Error sending credits added notification:", error);
  }
}

/**
 * Send low credits warning
 */
export async function notifyLowCredits(
  userUuid: string,
  params: {
    currentBalance: number;
    threshold: number;
    upgradeUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification(
      {
        accountId: userUuid,
        title: "Low Credits Warning",
        body: `Your credit balance is running low (${params.currentBalance} credits remaining). Consider upgrading your plan or purchasing more credits to continue using our services.`,
        type: "warning",
        channel: "both",
        link: params.upgradeUrl || "/pricing",
        emailTemplate: "notification",
        emailVariables: {
          actionUrl: params.upgradeUrl || "/pricing",
          actionText: "Upgrade Plan",
          ...params,
        },
      },
      db
    );
  } catch (error) {
    console.error("Error sending low credits notification:", error);
  }
}

/**
 * Send subscription cancelled notification
 */
export async function notifySubscriptionCancelled(
  userUuid: string,
  params: {
    planName: string;
    endDate: string;
    reactivateUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification(
      {
        accountId: userUuid,
        title: "Subscription Cancelled",
        body: `Your ${params.planName} subscription has been cancelled and will end on ${params.endDate}. You can reactivate it anytime before then.`,
        type: "warning",
        channel: "both",
        link: params.reactivateUrl || "/console/subscription",
        emailTemplate: "notification",
        emailVariables: {
          actionUrl: params.reactivateUrl || "/console/subscription",
          actionText: "Reactivate Subscription",
          ...params,
        },
      },
      db
    );
  } catch (error) {
    console.error("Error sending subscription cancelled notification:", error);
  }
}

/**
 * Send subscription reactivated notification
 */
export async function notifySubscriptionReactivated(
  userUuid: string,
  params: {
    planName: string;
    nextBillingDate: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification(
      {
        accountId: userUuid,
        title: "Subscription Reactivated",
        body: `Your ${params.planName} subscription has been reactivated. Your next billing date is ${params.nextBillingDate}.`,
        type: "success",
        channel: "both",
        link: "/console/subscription",
        emailTemplate: "notification",
        emailVariables: params,
      },
      db
    );
  } catch (error) {
    console.error("Error sending subscription reactivated notification:", error);
  }
}

/**
 * Send plan upgrade notification
 */
export async function notifyPlanUpgrade(
  userUuid: string,
  params: {
    fromPlan: string;
    toPlan: string;
    creditsAdded: number;
    effectiveDate: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification(
      {
        accountId: userUuid,
        title: "Plan Upgraded",
        body: `Your subscription has been upgraded from ${params.fromPlan} to ${params.toPlan}. ${params.creditsAdded} additional credits have been added to your account.`,
        type: "success",
        channel: "both",
        link: "/console/subscription",
        emailTemplate: "notification",
        emailVariables: params,
      },
      db
    );
  } catch (error) {
    console.error("Error sending plan upgrade notification:", error);
  }
}

/**
 * Send security alert notification
 */
export async function notifySecurityAlert(
  userUuid: string,
  params: {
    alertType: string;
    description: string;
    actionRequired?: boolean;
    actionUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification(
      {
        accountId: userUuid,
        title: `Security Alert: ${params.alertType}`,
        body: `${params.description}${params.actionRequired ? " Please review and take action if necessary." : ""}`,
        type: "security",
        channel: "both",
        link: params.actionUrl || "/console/settings",
        emailTemplate: "notification",
        emailVariables: {
          actionUrl: params.actionUrl || "/console/settings",
          actionText: params.actionRequired ? "Review Security" : "View Details",
          ...params,
        },
      },
      db
    );
  } catch (error) {
    console.error("Error sending security alert notification:", error);
  }
}

/**
 * Send system maintenance notification
 */
export async function notifySystemMaintenance(
  userUuid: string,
  params: {
    maintenanceType: string;
    startTime: string;
    endTime: string;
    affectedServices: string[];
    statusUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification(
      {
        accountId: userUuid,
        title: "Scheduled Maintenance",
        body: `${params.maintenanceType} is scheduled from ${params.startTime} to ${params.endTime}. Affected services: ${params.affectedServices.join(", ")}.`,
        type: "system",
        channel: "both",
        link: params.statusUrl || "/status",
        emailTemplate: "notification",
        emailVariables: {
          actionUrl: params.statusUrl || "/status",
          actionText: "View Status Page",
          ...params,
        },
        expiresAt: new Date(params.endTime),
      },
      db
    );
  } catch (error) {
    console.error("Error sending system maintenance notification:", error);
  }
}

/**
 * Send welcome notification for new users
 */
export async function notifyWelcome(
  userUuid: string,
  params: {
    name: string;
    startingCredits: number;
    gettingStartedUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification(
      {
        accountId: userUuid,
        title: "Welcome to Your App!",
        body: `Welcome ${params.name}! Your account has been created with ${params.startingCredits} free credits. Start exploring our AI-powered features today.`,
        type: "success",
        channel: "both",
        link: params.gettingStartedUrl || "/console/dashboard",
        emailTemplate: "welcome",
        emailVariables: params,
      },
      db
    );
  } catch (error) {
    console.error("Error sending welcome notification:", error);
  }
}

/**
 * Send API usage limit notification
 */
export async function notifyUsageLimit(
  userUuid: string,
  params: {
    limitType: "rate" | "quota";
    currentUsage: number;
    limit: number;
    resetTime?: string;
    upgradeUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    const title = params.limitType === "rate" ? "Rate Limit Reached" : "Usage Quota Exceeded";
    const body =
      params.limitType === "rate"
        ? `You've reached your API rate limit (${params.currentUsage}/${params.limit} requests). ${params.resetTime ? `Limit resets at ${params.resetTime}.` : ""}`
        : `You've exceeded your usage quota (${params.currentUsage}/${params.limit}). Consider upgrading your plan for higher limits.`;

    await createNotification(
      {
        accountId: userUuid,
        title,
        body,
        type: "warning",
        channel: "in_app",
        link: params.upgradeUrl || "/console/usage",
        emailTemplate: "notification",
        emailVariables: {
          actionUrl: params.upgradeUrl || "/console/usage",
          actionText: "View Usage",
          ...params,
        },
      },
      db
    );
  } catch (error) {
    console.error("Error sending usage limit notification:", error);
  }
}
