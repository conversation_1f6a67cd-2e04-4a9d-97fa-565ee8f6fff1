import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { createDbFromEnv, dbOperations } from "~/lib/db";

export async function loader({ request, context }: LoaderFunctionArgs) {
  const startTime = Date.now();

  try {
    // Basic health check response
    const health = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "2.0.0",
      environment: process.env.NODE_ENV || "unknown",
      checks: {
        database: { status: "unknown", responseTime: 0 },
        memory: { status: "healthy", usage: 0 },
        uptime: { status: "healthy", seconds: 0 },
      },
    };

    // Database health check
    try {
      const dbStartTime = Date.now();
      const db = context.db || createDbFromEnv();

      if (db) {
        const isHealthy = await dbOperations.health.checkConnection(db);
        const dbResponseTime = Date.now() - dbStartTime;

        health.checks.database = {
          status: isHealthy ? "healthy" : "unhealthy",
          responseTime: dbResponseTime,
        };
      } else {
        health.checks.database = {
          status: "unavailable",
          responseTime: 0,
        };
      }
    } catch (error) {
      health.checks.database = {
        status: "error",
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : "Unknown database error",
      };
    }

    // Memory usage (if available in Cloudflare Workers)
    try {
      if (typeof performance !== "undefined" && performance.memory) {
        const memoryUsage = performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize;
        health.checks.memory = {
          status: memoryUsage > 0.9 ? "warning" : "healthy",
          usage: Math.round(memoryUsage * 100),
        };
      }
    } catch (error) {
      // Memory info not available, keep default
    }

    // Calculate total response time
    const totalResponseTime = Date.now() - startTime;

    // Determine overall status
    const hasUnhealthyChecks = Object.values(health.checks).some(
      (check) => check.status === "unhealthy" || check.status === "error"
    );

    if (hasUnhealthyChecks) {
      health.status = "unhealthy";
    } else {
      const hasWarnings = Object.values(health.checks).some((check) => check.status === "warning");
      health.status = hasWarnings ? "warning" : "healthy";
    }

    // Add response time to health object
    (health as any).responseTime = totalResponseTime;

    // Return appropriate HTTP status
    const httpStatus = health.status === "healthy" ? 200 : health.status === "warning" ? 200 : 503;

    return json(health, { status: httpStatus });
  } catch (error) {
    console.error("Health check failed:", error);

    return json(
      {
        status: "error",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Health check failed",
        responseTime: Date.now() - startTime,
      },
      { status: 500 }
    );
  }
}

// Detailed health check with database statistics
export async function action({ request, context }: LoaderFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  const startTime = Date.now();

  try {
    const db = context.db || createDbFromEnv();
    if (!db) {
      return json(
        {
          status: "error",
          error: "Database connection not available",
          timestamp: new Date().toISOString(),
        },
        { status: 503 }
      );
    }

    // Get detailed database statistics
    const [connectionCheck, dbStats] = await Promise.allSettled([
      dbOperations.health.checkConnection(db),
      dbOperations.health.getStats(db),
    ]);

    const health = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      responseTime: Date.now() - startTime,
      database: {
        connection:
          connectionCheck.status === "fulfilled" && connectionCheck.value ? "healthy" : "unhealthy",
        statistics: dbStats.status === "fulfilled" ? dbStats.value : null,
        error: dbStats.status === "rejected" ? dbStats.reason?.message : null,
      },
      system: {
        memory:
          typeof performance !== "undefined" && performance.memory
            ? {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit,
              }
            : null,
        timestamp: new Date().toISOString(),
      },
    };

    // Determine overall status
    if (health.database.connection === "unhealthy") {
      health.status = "unhealthy";
    }

    const httpStatus = health.status === "healthy" ? 200 : 503;
    return json(health, { status: httpStatus });
  } catch (error) {
    console.error("Detailed health check failed:", error);

    return json(
      {
        status: "error",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Detailed health check failed",
        responseTime: Date.now() - startTime,
      },
      { status: 500 }
    );
  }
}
