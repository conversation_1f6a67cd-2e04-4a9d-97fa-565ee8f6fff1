import { Link } from "@remix-run/react";
import type { BrandProps } from "./types";

export function Brand({ title, url, logo }: BrandProps) {
  return (
    <Link to={url} className="flex items-center gap-3 group shrink-0">
      {logo ? (
        <img
          src={logo}
          alt={title}
          className="h-8 w-8 transition-transform group-hover:scale-105"
        />
      ) : (
        <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center transition-all group-hover:scale-105">
          <span className="text-primary-foreground font-bold text-sm">RS</span>
        </div>
      )}
      <div className="flex flex-col">
        <span className="text-lg font-bold text-foreground">{title}</span>
      </div>
    </Link>
  );
}
