/**
 * Google One Tap 修复工具
 * 自动诊断和修复常见的 Google 认证问题
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { useEffect, useState } from "react";

export const meta: MetaFunction = () => {
  return [
    { title: "Google Auth Fix Tool" },
    { name: "description", content: "Diagnose and fix Google One Tap issues" },
  ];
};

export async function loader({}: LoaderFunctionArgs) {
  return json({
    googleClientId: process.env.GOOGLE_CLIENT_ID,
    googleClientSecret: process.env.GOOGLE_CLIENT_SECRET ? "configured" : "not_configured",
    appUrl: process.env.APP_URL,
    nodeEnv: process.env.NODE_ENV,
  });
}

interface DiagnosticResult {
  test: string;
  status: "pass" | "fail" | "warning";
  message: string;
  solution?: string;
}

export default function GoogleAuthFix() {
  const { googleClientId, googleClientSecret, appUrl, nodeEnv } = useLoaderData<typeof loader>();
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [autoFixApplied, setAutoFixApplied] = useState<string[]>([]);

  const runDiagnostics = async () => {
    setIsRunning(true);
    setDiagnostics([]);
    const results: DiagnosticResult[] = [];

    // 测试 1: Client ID 配置
    if (!googleClientId) {
      results.push({
        test: "Google Client ID",
        status: "fail",
        message: "Google Client ID 未配置",
        solution: "在 .env 文件中设置 GOOGLE_CLIENT_ID"
      });
    } else if (!googleClientId.endsWith(".apps.googleusercontent.com")) {
      results.push({
        test: "Google Client ID Format",
        status: "fail",
        message: "Client ID 格式不正确",
        solution: "确保 Client ID 以 .apps.googleusercontent.com 结尾"
      });
    } else {
      results.push({
        test: "Google Client ID",
        status: "pass",
        message: "Client ID 配置正确"
      });
    }

    // 测试 2: Client Secret 配置
    if (googleClientSecret !== "configured") {
      results.push({
        test: "Google Client Secret",
        status: "warning",
        message: "Client Secret 未配置，OAuth 流程可能无法工作",
        solution: "在 .env 文件中设置 GOOGLE_CLIENT_SECRET"
      });
    } else {
      results.push({
        test: "Google Client Secret",
        status: "pass",
        message: "Client Secret 已配置"
      });
    }

    // 测试 3: 域名配置
    const currentOrigin = window.location.origin;
    if (appUrl !== currentOrigin) {
      results.push({
        test: "Domain Configuration",
        status: "warning",
        message: `APP_URL (${appUrl}) 与当前域名 (${currentOrigin}) 不匹配`,
        solution: "更新 .env 中的 APP_URL 或确保 Google Console 中包含两个域名"
      });
    } else {
      results.push({
        test: "Domain Configuration",
        status: "pass",
        message: "域名配置匹配"
      });
    }

    // 测试 4: Google API 可用性
    try {
      const response = await fetch("https://accounts.google.com/gsi/client", { 
        method: "HEAD",
        mode: "no-cors"
      });
      results.push({
        test: "Google API Connectivity",
        status: "pass",
        message: "可以连接到 Google API"
      });
    } catch (error) {
      results.push({
        test: "Google API Connectivity",
        status: "fail",
        message: "无法连接到 Google API",
        solution: "检查网络连接或防火墙设置"
      });
    }

    // 测试 5: 浏览器兼容性
    const userAgent = navigator.userAgent;
    if (userAgent.includes("Chrome") || userAgent.includes("Firefox") || userAgent.includes("Safari")) {
      results.push({
        test: "Browser Compatibility",
        status: "pass",
        message: "浏览器支持 Google One Tap"
      });
    } else {
      results.push({
        test: "Browser Compatibility",
        status: "warning",
        message: "浏览器可能不完全支持 Google One Tap",
        solution: "尝试使用 Chrome、Firefox 或 Safari"
      });
    }

    // 测试 6: HTTPS 状态
    if (window.location.protocol === "https:") {
      results.push({
        test: "HTTPS Status",
        status: "pass",
        message: "使用 HTTPS 协议"
      });
    } else {
      results.push({
        test: "HTTPS Status",
        status: "warning",
        message: "使用 HTTP 协议（开发环境正常）",
        solution: "生产环境需要使用 HTTPS"
      });
    }

    // 测试 7: Cookie 支持
    try {
      document.cookie = "test=1; Path=/";
      const cookieSupported = document.cookie.includes("test=1");
      document.cookie = "test=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT";
      
      if (cookieSupported) {
        results.push({
          test: "Cookie Support",
          status: "pass",
          message: "浏览器支持 Cookie"
        });
      } else {
        results.push({
          test: "Cookie Support",
          status: "fail",
          message: "浏览器不支持 Cookie",
          solution: "启用浏览器 Cookie 支持"
        });
      }
    } catch (error) {
      results.push({
        test: "Cookie Support",
        status: "fail",
        message: "Cookie 测试失败",
        solution: "检查浏览器 Cookie 设置"
      });
    }

    setDiagnostics(results);
    setIsRunning(false);
  };

  const applyAutoFix = (test: string) => {
    switch (test) {
      case "Domain Configuration":
        // 自动更新环境变量建议
        const currentOrigin = window.location.origin;
        const suggestion = `更新 .env 文件:\nAPP_URL="${currentOrigin}"\nALLOWED_ORIGINS="${currentOrigin},https://your-app.vercel.app"`;
        navigator.clipboard.writeText(suggestion);
        setAutoFixApplied(prev => [...prev, test]);
        alert("配置建议已复制到剪贴板");
        break;
      
      case "Google API Connectivity":
        // 重新加载 Google 脚本
        const existingScript = document.querySelector('script[src="https://accounts.google.com/gsi/client"]');
        if (existingScript) existingScript.remove();
        
        const script = document.createElement("script");
        script.src = "https://accounts.google.com/gsi/client";
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);
        
        setAutoFixApplied(prev => [...prev, test]);
        alert("Google 脚本已重新加载");
        break;
        
      default:
        alert("此问题需要手动修复");
    }
  };

  const openGoogleConsole = () => {
    const url = `https://console.developers.google.com/apis/credentials`;
    window.open(url, "_blank");
  };

  const testGoogleOneTap = () => {
    window.location.href = "/test/simple-google";
  };

  const testTraditionalOAuth = () => {
    window.location.href = "/auth/google/oauth";
  };

  useEffect(() => {
    // 自动运行诊断
    runDiagnostics();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Google 认证修复工具</h1>

        {/* 快速操作 */}
        <div className="bg-white p-6 rounded-lg shadow mb-8">
          <h2 className="text-xl font-semibold mb-4">快速操作</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button
              onClick={runDiagnostics}
              disabled={isRunning}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
            >
              {isRunning ? "诊断中..." : "重新诊断"}
            </button>
            
            <button
              onClick={openGoogleConsole}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              打开 Google Console
            </button>
            
            <button
              onClick={testGoogleOneTap}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            >
              测试 One Tap
            </button>
            
            <button
              onClick={testTraditionalOAuth}
              className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
            >
              测试传统 OAuth
            </button>
          </div>
        </div>

        {/* 诊断结果 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">诊断结果</h2>
          
          {diagnostics.length === 0 ? (
            <div className="text-gray-500">运行诊断以查看结果...</div>
          ) : (
            <div className="space-y-4">
              {diagnostics.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className={`w-3 h-3 rounded-full ${
                        result.status === "pass" ? "bg-green-500" :
                        result.status === "warning" ? "bg-yellow-500" : "bg-red-500"
                      }`} />
                      <div>
                        <h3 className="font-medium">{result.test}</h3>
                        <p className="text-sm text-gray-600">{result.message}</p>
                        {result.solution && (
                          <p className="text-sm text-blue-600 mt-1">💡 {result.solution}</p>
                        )}
                      </div>
                    </div>
                    
                    {result.status !== "pass" && result.solution && (
                      <button
                        onClick={() => applyAutoFix(result.test)}
                        disabled={autoFixApplied.includes(result.test)}
                        className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:bg-gray-100 disabled:text-gray-500"
                      >
                        {autoFixApplied.includes(result.test) ? "已应用" : "自动修复"}
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 配置指南 */}
        <div className="bg-white p-6 rounded-lg shadow mt-8">
          <h2 className="text-xl font-semibold mb-4">Google Console 配置指南</h2>
          <div className="space-y-4 text-sm">
            <div>
              <h3 className="font-medium">1. 授权的 JavaScript 来源</h3>
              <div className="bg-gray-100 p-2 rounded font-mono text-xs mt-1">
                {window.location.origin}<br/>
                http://localhost:5173<br/>
                http://localhost:3000
              </div>
            </div>
            
            <div>
              <h3 className="font-medium">2. 授权的重定向 URI</h3>
              <div className="bg-gray-100 p-2 rounded font-mono text-xs mt-1">
                {window.location.origin}/auth/google/callback<br/>
                {window.location.origin}/auth/google/oauth
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
