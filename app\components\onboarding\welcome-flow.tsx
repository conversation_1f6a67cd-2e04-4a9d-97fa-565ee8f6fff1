/**
 * User Onboarding Welcome Flow Component
 * Guides new users through their first experience
 */

// Removed framer-motion dependency for smaller bundle size
import {
  ArrowLeft,
  ArrowRight,
  CreditCard,
  Image,
  MessageCircle,
  Sparkles,
  Users,
  X,
} from "lucide-react";
import { useEffect, useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

interface WelcomeFlowProps {
  user: {
    name: string;
    email: string;
    credits: number;
    inviteCode?: string;
  };
  isNewUser?: boolean;
  onComplete: () => void;
  onSkip: () => void;
}

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  content: React.ReactNode;
  action?: {
    label: string;
    href?: string;
    onClick?: () => void;
  };
}

export default function WelcomeFlow({
  user,
  isNewUser = false,
  onComplete,
  onSkip,
}: WelcomeFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(isNewUser);

  const steps: OnboardingStep[] = [
    {
      id: "welcome",
      title: `Welcome, ${user.name}! 🎉`,
      description: "You're all set to start building with AI",
      icon: <Sparkles className="w-8 h-8 text-purple-500" />,
      content: (
        <div className="text-center space-y-4">
          <div className="bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg p-6">
            <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
              {user.credits} Credits
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
              Free credits added to your account!
            </p>
            {user.inviteCode && (
              <Badge variant="secondary" className="mt-2">
                🎁 Invite bonus included!
              </Badge>
            )}
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Start experimenting with our AI tools right away - no payment required!
          </p>
        </div>
      ),
    },
    {
      id: "features",
      title: "Powerful AI Tools at Your Fingertips",
      description: "Explore what you can build with our platform",
      icon: <MessageCircle className="w-8 h-8 text-blue-500" />,
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <MessageCircle className="w-5 h-5 text-blue-500" />
                <h4 className="font-semibold">AI Chat</h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Conversations with GPT-4, Claude, and more
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <Image className="w-5 h-5 text-green-500" />
                <h4 className="font-semibold">Image Generation</h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Create stunning visuals with DALL-E & Stable Diffusion
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <CreditCard className="w-5 h-5 text-purple-500" />
                <h4 className="font-semibold">Usage Tracking</h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Monitor your credits and usage in real-time
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <Users className="w-5 h-5 text-orange-500" />
                <h4 className="font-semibold">Team Collaboration</h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Share and collaborate with your team
              </p>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "quickstart",
      title: "Quick Start Guide",
      description: "Get up and running in minutes",
      icon: <ArrowRight className="w-8 h-8 text-green-500" />,
      content: (
        <div className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                1
              </div>
              <div>
                <h4 className="font-semibold">Start a Conversation</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Go to your dashboard and try the AI chat interface
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                2
              </div>
              <div>
                <h4 className="font-semibold">Generate Images</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Use the "/image" command in chat to create visuals
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                3
              </div>
              <div>
                <h4 className="font-semibold">Monitor Usage</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Check your credits and usage in the dashboard
                </p>
              </div>
            </div>
          </div>
          {user.inviteCode && (
            <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg">
              <h4 className="font-semibold text-green-700 dark:text-green-400 mb-2">
                🎁 Share & Earn
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Your invite code:{" "}
                <code className="bg-white dark:bg-gray-800 px-2 py-1 rounded">
                  {user.inviteCode}
                </code>
              </p>
              <p className="text-xs text-gray-500">Share with friends to earn bonus credits!</p>
            </div>
          )}
        </div>
      ),
      action: {
        label: "Start Building Now",
        href: "/console/dashboard",
      },
    },
  ];

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    setIsVisible(false);
    setTimeout(onComplete, 300);
  };

  const handleSkip = () => {
    setIsVisible(false);
    setTimeout(onSkip, 300);
  };

  if (!isVisible) return null;

  const currentStepData = steps[currentStep];

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fade-in">
      <div className="w-full max-w-2xl animate-slide-up">
        <Card className="relative">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSkip}
            className="absolute top-4 right-4 z-10"
          >
            <X className="w-4 h-4" />
          </Button>

          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">{currentStepData.icon}</div>
            <CardTitle className="text-2xl">{currentStepData.title}</CardTitle>
            <CardDescription>{currentStepData.description}</CardDescription>

            {/* Progress indicator */}
            <div className="flex justify-center space-x-2 mt-4">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentStep
                      ? "bg-blue-500"
                      : index < currentStep
                        ? "bg-green-500"
                        : "bg-gray-300"
                  }`}
                />
              ))}
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            <div key={currentStep} className="animate-fade-in">
              {currentStepData.content}
            </div>

            <div className="flex justify-between items-center pt-4">
              <Button
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 0}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Previous</span>
              </Button>

              <div className="flex space-x-2">
                <Button variant="ghost" onClick={handleSkip}>
                  Skip Tour
                </Button>
                {currentStepData.action ? (
                  <Button
                    onClick={currentStepData.action.onClick || handleComplete}
                    className="flex items-center space-x-2"
                  >
                    <span>{currentStepData.action.label}</span>
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                ) : (
                  <Button onClick={nextStep} className="flex items-center space-x-2">
                    <span>Next</span>
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
