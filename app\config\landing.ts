export interface LandingPageConfig {
  hero: {
    title: string;
    highlight_text: string;
    description: string;
    announcement?: {
      title: string;
      url: string;
      label?: string;
    };
    buttons: Array<{
      title: string;
      url: string;
      variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
      icon?: string;
    }>;
    tip?: string;
    show_badge?: boolean;
    show_happy_users?: boolean;
  };
  branding: {
    title: string;
    description: string;
    items: Array<{
      name: string;
      logo: string;
      url?: string;
    }>;
  };
  introduce: {
    title: string;
    description: string;
    content: string;
    image?: string;
    video?: string;
    buttons?: Array<{
      title: string;
      url: string;
      variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
    }>;
    features?: string[];
    reversed?: boolean;
  };
  benefits: {
    title: string;
    description: string;
    items: Array<{
      title: string;
      description: string;
      icon?: string;
    }>;
  };
  features: {
    title: string;
    description: string;
    items: Array<{
      title: string;
      description: string;
      icon: string;
      image?: string;
    }>;
  };
  showcase: {
    title: string;
    description: string;
    items: Array<{
      title: string;
      description: string;
      image: string;
      url?: string;
    }>;
  };
  usage: {
    title: string;
    description: string;
    items: Array<{
      title: string;
      description: string;
      icon: string;
    }>;
  };
  stats: {
    title: string;
    description: string;
    items: Array<{
      label: string;
      value: string;
      description: string;
    }>;
  };
  testimonials: {
    title: string;
    description: string;
    items: Array<{
      name: string;
      role: string;
      content: string;
      avatar?: string;
    }>;
  };
  faq: {
    title: string;
    description: string;
    items: Array<{
      question: string;
      answer: string;
    }>;
  };
  cta: {
    title: string;
    description: string;
    buttons: Array<{
      title: string;
      url: string;
      variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
    }>;
  };
}

export function getLandingPageConfig(): LandingPageConfig {
  return {
    hero: {
      title: "Build Amazing Applications with Remix",
      highlight_text: "Remix",
      description:
        "A modern starter template with Remix, Cloudflare, Neon Database, and powerful AI tools integration. Get started in minutes, not months.",
      announcement: {
        title: "🎉 Full-Stack Remix Template Available!",
        url: "/",
        label: "New",
      },
      buttons: [
        {
          title: "🚀 Get Started",
          url: "/ai-tools",
          variant: "default",
        },
        {
          title: "📚 View Components",
          url: "/components",
          variant: "outline",
        },
      ],
      tip: "✨ No credit card required • Free tier available",
      show_badge: true,
      show_happy_users: true,
    },
    branding: {
      title: "Trusted by Leading Companies",
      description:
        "Join thousands of developers and companies who trust our platform to build their next-generation applications.",
      items: [
        {
          name: "Remix",
          logo: "/logo-light.png",
          url: "https://remix.run",
        },
        {
          name: "Cloudflare",
          logo: "/logo-light.png",
          url: "https://cloudflare.com",
        },
        {
          name: "Neon",
          logo: "/logo-light.png",
          url: "https://neon.tech",
        },
        {
          name: "OpenAI",
          logo: "/logo-light.png",
          url: "https://openai.com",
        },
        {
          name: "Stripe",
          logo: "/logo-light.png",
          url: "https://stripe.com",
        },
        {
          name: "Tailwind CSS",
          logo: "/logo-light.png",
          url: "https://tailwindcss.com",
        },
      ],
    },
    introduce: {
      title: "The Complete Full-Stack Solution",
      description: "Everything you need to build, deploy, and scale your application.",
      content:
        "Our comprehensive starter template combines the best modern web technologies with AI-powered features to help you launch your product faster than ever. Built with Remix for performance, Cloudflare for global scale, and integrated with powerful AI tools for next-generation functionality.",
      image: "/images/hero-bg.svg",
      buttons: [
        {
          title: "Explore Features",
          url: "/components",
          variant: "default",
        },
        {
          title: "View Demo",
          url: "/ai-tools",
          variant: "outline",
        },
      ],
      features: [
        "Full-stack Remix application with TypeScript",
        "Cloudflare Workers deployment for global edge performance",
        "Neon database with branching and autoscaling",
        "Multiple AI provider integrations (OpenAI, DeepSeek, etc.)",
        "Complete authentication and user management",
        "Stripe payment integration with subscription management",
      ],
    },
    benefits: {
      title: "Why Choose Our Starter Template",
      description:
        "Discover the advantages that make our template the perfect choice for your next project.",
      items: [
        {
          title: "Rapid Development",
          description:
            "Launch your application in days, not months. Pre-built components and integrations accelerate your development process.",
          icon: "⚡",
        },
        {
          title: "Production Ready",
          description:
            "Built with best practices for security, performance, and scalability. Ready to handle real-world traffic from day one.",
          icon: "🚀",
        },
        {
          title: "AI-Powered Features",
          description:
            "Integrated AI tools and multiple provider support. Add intelligent features to your application effortlessly.",
          icon: "🤖",
        },
        {
          title: "Global Scale",
          description:
            "Deploy to Cloudflare's global edge network. Serve your users with minimal latency worldwide.",
          icon: "🌍",
        },
        {
          title: "Modern Stack",
          description:
            "Built with the latest technologies: Remix, TypeScript, Tailwind CSS, and modern development practices.",
          icon: "💎",
        },
        {
          title: "Complete Documentation",
          description:
            "Comprehensive guides, examples, and documentation to help you customize and extend the template.",
          icon: "📚",
        },
      ],
    },
    features: {
      title: "Everything You Need to Build Modern Applications",
      description:
        "A complete starter template with all the tools and integrations you need to launch your product quickly.",
      items: [
        {
          title: "AI Integration",
          description:
            "Multiple AI providers with streaming support. OpenAI, DeepSeek, OpenRouter, and more. Text generation, streaming, and image creation.",
          icon: "/images/icons/ai.svg",
        },
        {
          title: "Remix Framework",
          description:
            "Server-side rendering, nested routing, and progressive enhancement out of the box.",
          icon: "/images/icons/remix.svg",
        },
        {
          title: "Cloudflare Edge",
          description: "Global edge deployment with zero cold starts and unlimited scalability.",
          icon: "/images/icons/cloudflare.svg",
        },
        {
          title: "Neon Database",
          description: "Branching, autoscaling, and point-in-time recovery for your data.",
          icon: "/images/icons/database.svg",
        },
        {
          title: "Beautiful UI",
          description: "Accessible, customizable components with dark mode support.",
          icon: "/images/icons/ui.svg",
        },
        {
          title: "Payments Ready",
          description: "Complete payment flow with subscription management and webhooks.",
          icon: "/images/icons/payments.svg",
        },
      ],
    },
    showcase: {
      title: "Built with Modern Technologies",
      description: "See what you can build with our comprehensive starter template.",
      items: [
        {
          title: "AI Chat Interface",
          description: "Interactive AI chat with streaming responses and multiple providers",
          image: "/images/showcase/ai-chat.svg",
          url: "/ai-tools",
        },
        {
          title: "Database Analytics",
          description: "Real-time Neon database monitoring with performance insights",
          image: "/images/showcase/database.svg",
          url: "/test-db",
        },
        {
          title: "Dark Mode Support",
          description: "Seamless theme switching with automatic system preference detection",
          image: "/images/showcase/dark-mode.svg",
          url: "/components",
        },
        {
          title: "Authentication Flow",
          description: "Secure login with OAuth providers and session management",
          image: "/images/showcase/auth.svg",
          url: "/components",
        },
        {
          title: "Payment Integration",
          description: "Complete Stripe integration with subscription management",
          image: "/images/showcase/payments.svg",
          url: "/checkout",
        },
      ],
    },
    usage: {
      title: "Get Started in 3 Simple Steps",
      description: "Launch your application quickly with our streamlined process.",
      items: [
        {
          title: "Clone & Setup",
          description:
            "Clone the repository and install dependencies with pnpm. Configure your environment variables.",
          icon: "/images/icons/download.svg",
        },
        {
          title: "Customize",
          description:
            "Modify the template with your branding, content, and specific AI functionality needs.",
          icon: "/images/icons/customize.svg",
        },
        {
          title: "Deploy",
          description:
            "Deploy to Cloudflare Workers with a single command and start serving customers.",
          icon: "/images/icons/deploy.svg",
        },
      ],
    },
    stats: {
      title: "Trusted by Developers Worldwide",
      description: "Join thousands of developers who have launched successful applications.",
      items: [
        {
          label: "1000+",
          value: "Developers",
          description: "Using our starter template",
        },
        {
          label: "50+",
          value: "Components",
          description: "Ready-to-use UI components",
        },
        {
          label: "99.9%",
          value: "Uptime",
          description: "Reliable infrastructure",
        },
      ],
    },
    testimonials: {
      title: "What Developers Say",
      description:
        "Hear from developers who have built amazing applications with our starter template.",
      items: [
        {
          name: "Alex Chen",
          role: "Full-stack Developer",
          content:
            "This starter template saved me months of development time. The AI integration is seamless and the code quality is excellent.",
          avatar: "/images/avatars/alex.svg",
        },
        {
          name: "Sarah Johnson",
          role: "Startup Founder",
          content:
            "Perfect for launching an MVP quickly. The Cloudflare integration and edge deployment made scaling effortless.",
          avatar: "/images/avatars/sarah.svg",
        },
        {
          name: "Mike Rodriguez",
          role: "Senior Engineer",
          content:
            "The best Remix starter I've used. Clean architecture, great documentation, and excellent developer experience.",
          avatar: "/images/avatars/mike.svg",
        },
      ],
    },
    faq: {
      title: "Frequently Asked Questions",
      description: "Everything you need to know about our Remix starter template.",
      items: [
        {
          question: "What's included in the starter template?",
          answer:
            "The template includes Remix framework, Cloudflare Workers deployment, Neon database integration, AI tools, authentication, payments with Stripe, UI components, and comprehensive documentation.",
        },
        {
          question: "Do I need experience with Remix to use this?",
          answer:
            "Basic React knowledge is helpful, but our comprehensive documentation and examples make it easy to get started even if you're new to Remix.",
        },
        {
          question: "Can I customize the AI integrations?",
          answer:
            "Absolutely! The template supports multiple AI providers and you can easily add new ones or modify existing integrations to fit your specific needs.",
        },
        {
          question: "Is the template production-ready?",
          answer:
            "Yes, the template follows best practices for security, performance, and scalability. It's designed to handle production workloads from day one.",
        },
        {
          question: "What kind of support do you provide?",
          answer:
            "We provide comprehensive documentation, example code, and community support. Premium support options are also available for enterprise customers.",
        },
      ],
    },
    cta: {
      title: "Ready to Build Your Application?",
      description:
        "Start building your next successful application today with our comprehensive starter template.",
      buttons: [
        {
          title: "Get Started Now",
          url: "/ai-tools",
          variant: "default",
        },
        {
          title: "View Documentation",
          url: "/docs",
          variant: "outline",
        },
      ],
    },
  };
}
