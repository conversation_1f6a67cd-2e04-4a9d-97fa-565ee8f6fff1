/**
 * Security API
 * Provides security management and monitoring endpoints
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { respData, respErr } from "~/lib/api/resp";
import { createDbFromEnv } from "~/lib/db";
import {
  type AuditEventType,
  type AuditSeverity,
  clearOldAuditLogs,
  getAuditEvents,
  getSecurityMetrics,
  logAuditEvent,
} from "~/lib/security/audit-logger.server";
import { ValidationSchemas, validateAndSanitize } from "~/lib/security/input-validation.server";
import {
  createPermissionErrorResponse,
  getUserRole,
  hasPermission,
} from "~/lib/security/permissions.server";
import {
  clearAllRateLimits,
  getAllRateLimitEntries,
  RATE_LIMIT_CONFIGS,
  resetRateLimit,
} from "~/lib/security/rate-limiter.server";
import {
  applySecurity,
  DEFAULT_SECURITY_CONFIG,
  detectThreats,
  extractSecurityContext,
} from "~/lib/security/security-middleware.server";
import { getUserUuid } from "~/services/user-management.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv();

    const url = new URL(request.url);
    const action = url.searchParams.get("action");

    // Get current user
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Authentication required");
    }

    // Check admin permissions
    const userRole = getUserRole(userUuid);
    const permissionCheck = hasPermission(userRole, "admin:security");
    if (!permissionCheck.allowed) {
      return createPermissionErrorResponse(permissionCheck);
    }

    switch (action) {
      case "metrics": {
        const metrics = getSecurityMetrics();
        return respData({ metrics });
      }

      case "audit-events": {
        const limit = parseInt(url.searchParams.get("limit") || "50", 10);
        const offset = parseInt(url.searchParams.get("offset") || "0", 10);
        const eventType = url.searchParams.get("event_type") as AuditEventType | undefined;
        const severity = url.searchParams.get("severity") as AuditSeverity | undefined;
        const ipAddress = url.searchParams.get("ip_address") || undefined;
        const startDate = url.searchParams.get("start_date")
          ? new Date(url.searchParams.get("start_date")!)
          : undefined;
        const endDate = url.searchParams.get("end_date")
          ? new Date(url.searchParams.get("end_date")!)
          : undefined;

        const events = getAuditEvents({
          limit,
          offset,
          eventType,
          severity,
          ipAddress,
          startDate,
          endDate,
        });

        return respData({ events, total: events.length });
      }

      case "rate-limits": {
        const rateLimitEntries = getAllRateLimitEntries();
        const rateLimitData = Array.from(rateLimitEntries.entries()).map(([key, entry]) => ({
          key,
          count: entry.count,
          resetTime: new Date(entry.resetTime).toISOString(),
          requests: entry.requests.length,
        }));

        const stats = {
          totalKeys: rateLimitEntries.size,
          activeKeys: Array.from(rateLimitEntries.values()).filter((entry) => entry.count > 0)
            .length,
          totalRequests: Array.from(rateLimitEntries.values()).reduce(
            (sum, entry) => sum + entry.count,
            0
          ),
        };

        return respData({ rateLimits: rateLimitData, stats });
      }

      case "security-config": {
        return respData({ config: DEFAULT_SECURITY_CONFIG });
      }

      case "threat-check": {
        const input = url.searchParams.get("input");
        if (!input) {
          return respErr("Input parameter required");
        }

        const threats = detectThreats(request, input);
        return respData({ threats, input });
      }

      case "validate-input": {
        const input = url.searchParams.get("input");
        const schema = url.searchParams.get("schema") || "content";

        if (!input) {
          return respErr("Input parameter required");
        }

        let validationSchema;
        switch (schema) {
          case "email":
            validationSchema = ValidationSchemas.email;
            break;
          case "password":
            validationSchema = ValidationSchemas.password;
            break;
          case "name":
            validationSchema = ValidationSchemas.name;
            break;
          case "url":
            validationSchema = ValidationSchemas.url;
            break;
          default:
            validationSchema = ValidationSchemas.content;
        }

        const result = await validateAndSanitize(input, validationSchema, {
          allowHtml: false,
          maxLength: 1000,
        });

        return respData({ validation: result });
      }

      case "system-status": {
        const status = {
          rateLimiting: true,
          inputValidation: true,
          auditLogging: true,
          threatDetection: true,
          securityHeaders: true,
          timestamp: new Date().toISOString(),
        };

        return respData({ status });
      }

      default:
        return respErr(
          "Invalid action. Supported actions: metrics, audit-events, rate-limits, security-config, threat-check, validate-input, system-status"
        );
    }
  } catch (error) {
    console.error("Error in security API:", error);
    return respErr(error instanceof Error ? error.message : "Failed to process security request");
  }
}

export async function action({ request, context }: ActionFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv();

    // Get current user
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Authentication required");
    }

    // Check admin permissions
    const userRole = getUserRole(userUuid);
    const permissionCheck = hasPermission(userRole, "admin:security");
    if (!permissionCheck.allowed) {
      return createPermissionErrorResponse(permissionCheck);
    }

    // Apply security middleware
    const securityResult = await applySecurity(request, DEFAULT_SECURITY_CONFIG, userUuid);
    if (!securityResult.allowed) {
      return respErr(securityResult.reason || "Security check failed");
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case "log-event": {
        const { eventType, severity, eventAction, details } = body;

        if (!eventType || !severity || !eventAction) {
          return respErr("Missing required fields: eventType, severity, action");
        }

        const context = extractSecurityContext(request, userUuid);

        await logAuditEvent({
          eventType,
          severity,
          userUuid,
          ipAddress: context.ip,
          userAgent: context.userAgent,
          endpoint: context.endpoint,
          method: context.method,
          action: eventAction,
          success: true,
          details,
        });

        return respData({ message: "Event logged successfully" });
      }

      case "clear-rate-limits": {
        clearAllRateLimits();

        await logAuditEvent({
          eventType: "ADMIN_ACTION",
          severity: "MEDIUM",
          userUuid,
          ipAddress: extractSecurityContext(request, userUuid).ip,
          userAgent: request.headers.get("user-agent") || "unknown",
          endpoint: new URL(request.url).pathname,
          method: request.method,
          action: "CLEAR_RATE_LIMITS",
          success: true,
        });

        return respData({ message: "All rate limits cleared successfully" });
      }

      case "reset-rate-limit": {
        const { key } = body;

        if (!key) {
          return respErr("Rate limit key required");
        }

        // Create a mock request for the key
        const endpoint = key.split(":")[1] || "/";
        const mockRequest = new Request(`https://example.com${endpoint}`, {
          method: "GET",
        });

        await resetRateLimit(mockRequest, RATE_LIMIT_CONFIGS.api, userUuid);

        await logAuditEvent({
          eventType: "ADMIN_ACTION",
          severity: "LOW",
          userUuid,
          ipAddress: extractSecurityContext(request, userUuid).ip,
          userAgent: request.headers.get("user-agent") || "unknown",
          endpoint: new URL(request.url).pathname,
          method: request.method,
          action: "RESET_RATE_LIMIT",
          success: true,
          details: { key },
        });

        return respData({ message: "Rate limit reset successfully" });
      }

      case "clear-audit-logs": {
        const { olderThanDays } = body;
        const days = olderThanDays || 30;

        const removedCount = clearOldAuditLogs(days);

        await logAuditEvent({
          eventType: "ADMIN_ACTION",
          severity: "MEDIUM",
          userUuid,
          ipAddress: extractSecurityContext(request, userUuid).ip,
          userAgent: request.headers.get("user-agent") || "unknown",
          endpoint: new URL(request.url).pathname,
          method: request.method,
          action: "CLEAR_AUDIT_LOGS",
          success: true,
          details: { olderThanDays: days, removedCount },
        });

        return respData({
          message: `Cleared ${removedCount} old audit logs`,
          removedCount,
        });
      }

      case "block-ip": {
        const { ipAddress, reason } = body;

        if (!ipAddress) {
          return respErr("IP address required");
        }

        // TODO: Implement IP blocking functionality
        // This would typically add the IP to a blocked list

        await logAuditEvent({
          eventType: "ADMIN_ACTION",
          severity: "HIGH",
          userUuid,
          ipAddress: extractSecurityContext(request, userUuid).ip,
          userAgent: request.headers.get("user-agent") || "unknown",
          endpoint: new URL(request.url).pathname,
          method: request.method,
          action: "BLOCK_IP",
          success: true,
          details: { blockedIp: ipAddress, reason },
        });

        return respData({
          message: `IP ${ipAddress} blocked successfully`,
          blockedIp: ipAddress,
        });
      }

      case "unblock-ip": {
        const { ipAddress } = body;

        if (!ipAddress) {
          return respErr("IP address required");
        }

        // TODO: Implement IP unblocking functionality

        await logAuditEvent({
          eventType: "ADMIN_ACTION",
          severity: "MEDIUM",
          userUuid,
          ipAddress: extractSecurityContext(request, userUuid).ip,
          userAgent: request.headers.get("user-agent") || "unknown",
          endpoint: new URL(request.url).pathname,
          method: request.method,
          action: "UNBLOCK_IP",
          success: true,
          details: { unblockedIp: ipAddress },
        });

        return respData({
          message: `IP ${ipAddress} unblocked successfully`,
          unblockedIp: ipAddress,
        });
      }

      default:
        return respErr(
          "Invalid action. Supported actions: log-event, clear-rate-limits, reset-rate-limit, clear-audit-logs, block-ip, unblock-ip"
        );
    }
  } catch (error) {
    console.error("Error in security API action:", error);
    return respErr(error instanceof Error ? error.message : "Failed to process security action");
  }
}
