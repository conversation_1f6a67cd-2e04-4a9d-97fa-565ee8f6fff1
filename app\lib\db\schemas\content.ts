// Content management related schemas (posts, feedback)
import { relations } from "drizzle-orm";
import {
  boolean,
  index,
  integer,
  jsonb,
  pgEnum,
  pgTable,
  uuid as pgUuid,
  serial,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";
import { users } from "./users";

// Feedback enums
export const feedbackTypeEnum = pgEnum("feedback_type", [
  "bug_report",
  "feature_request",
  "improvement",
  "question",
  "complaint",
  "compliment",
  "other",
]);

export const feedbackPriorityEnum = pgEnum("feedback_priority", [
  "low",
  "medium",
  "high",
  "urgent",
]);

export const feedbackStatusEnum = pgEnum("feedback_status", [
  "open",
  "in_progress",
  "resolved",
  "closed",
  "duplicate",
]);

// Posts table (for blog/content)
export const posts = pgTable(
  "posts",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    title: varchar("title", { length: 255 }).notNull(),
    slug: varchar("slug", { length: 255 }).notNull().unique(),
    content: text("content"),
    excerpt: text("excerpt"),
    authorId: pgUuid("author_id").references(() => users.id),
    published: boolean("published").default(false).notNull(),
    publishedAt: timestamp("published_at"),
    tags: text("tags"), // JSON array of tags
    metadata: jsonb("metadata"), // SEO and other metadata
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    slugIdx: index("posts_slug_idx").on(table.slug),
    authorIdx: index("posts_author_idx").on(table.authorId),
    publishedIdx: index("posts_published_idx").on(table.published),
    publishedAtIdx: index("posts_published_at_idx").on(table.publishedAt),
  })
);

// Feedback table
export const feedback = pgTable(
  "feedback",
  {
    id: serial("id").primaryKey(),
    userId: pgUuid("user_id").references(() => users.id, { onDelete: "set null" }),
    type: feedbackTypeEnum("type").notNull(),
    priority: feedbackPriorityEnum("priority").notNull().default("medium"),
    status: feedbackStatusEnum("status").notNull().default("open"),
    title: varchar("title", { length: 255 }).notNull(),
    description: text("description").notNull(),
    email: varchar("email", { length: 255 }), // For anonymous feedback
    userAgent: text("user_agent"),
    url: varchar("url", { length: 500 }), // Page where feedback was submitted
    attachments: jsonb("attachments"), // File attachments
    adminNotes: text("admin_notes"), // Internal notes
    resolvedAt: timestamp("resolved_at"),
    resolvedBy: pgUuid("resolved_by").references(() => users.id),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdx: index("feedback_user_idx").on(table.userId),
    typeIdx: index("feedback_type_idx").on(table.type),
    statusIdx: index("feedback_status_idx").on(table.status),
    priorityIdx: index("feedback_priority_idx").on(table.priority),
    createdIdx: index("feedback_created_idx").on(table.createdAt),
    emailIdx: index("feedback_email_idx").on(table.email),
  })
);

// Relations
export const postsRelations = relations(posts, ({ one }) => ({
  author: one(users, {
    fields: [posts.authorId],
    references: [users.id],
  }),
}));

export const feedbackRelations = relations(feedback, ({ one }) => ({
  user: one(users, {
    fields: [feedback.userId],
    references: [users.id],
  }),
  resolvedBy: one(users, {
    fields: [feedback.resolvedBy],
    references: [users.id],
  }),
}));
