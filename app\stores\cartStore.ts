import { create } from "zustand";
import { createJSONStorage, devtools, persist } from "zustand/middleware";
import type { CartItem, CartState } from "./types";

const initialState = {
  items: [],
  total: 0,
  isOpen: false,
};

const calculateTotal = (items: CartItem[]) => {
  return items.reduce((sum, item) => sum + item.price * item.quantity, 0);
};

export const useCartStore = create<CartState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        addItem: (newItem) => {
          set(
            (state) => {
              const existingItem = state.items.find((item) => item.id === newItem.id);
              let updatedItems;

              if (existingItem) {
                updatedItems = state.items.map((item) =>
                  item.id === newItem.id ? { ...item, quantity: item.quantity + 1 } : item
                );
              } else {
                updatedItems = [...state.items, { ...newItem, quantity: 1 }];
              }

              return {
                items: updatedItems,
                total: calculateTotal(updatedItems),
              };
            },
            false,
            "addItem"
          );
        },

        removeItem: (id) => {
          set(
            (state) => {
              const updatedItems = state.items.filter((item) => item.id !== id);
              return {
                items: updatedItems,
                total: calculateTotal(updatedItems),
              };
            },
            false,
            "removeItem"
          );
        },

        updateQuantity: (id, quantity) => {
          set(
            (state) => {
              if (quantity <= 0) {
                const updatedItems = state.items.filter((item) => item.id !== id);
                return {
                  items: updatedItems,
                  total: calculateTotal(updatedItems),
                };
              }

              const updatedItems = state.items.map((item) =>
                item.id === id ? { ...item, quantity } : item
              );

              return {
                items: updatedItems,
                total: calculateTotal(updatedItems),
              };
            },
            false,
            "updateQuantity"
          );
        },

        clearCart: () => {
          set(
            {
              items: [],
              total: 0,
            },
            false,
            "clearCart"
          );
        },

        toggleCart: () => {
          set((state) => ({ isOpen: !state.isOpen }), false, "toggleCart");
        },

        setCartOpen: (open) => {
          set({ isOpen: open }, false, "setCartOpen");
        },

        reset: () => {
          set(initialState, false, "reset");
        },
      }),
      {
        name: "cart-store",
        storage: createJSONStorage(() => {
          if (typeof window !== "undefined") {
            return localStorage;
          }
          // Return a no-op storage for server-side
          return {
            getItem: () => null,
            setItem: () => {},
            removeItem: () => {},
          };
        }),
        partialize: (state) => ({
          items: state.items,
          total: state.total,
        }),
        skipHydration: typeof window === "undefined",
      }
    ),
    {
      name: "cart-store",
    }
  )
);

// Selector functions
export const selectCartItems = (state: CartState) => state.items;
export const selectCartTotal = (state: CartState) => state.total;
export const selectCartIsOpen = (state: CartState) => state.isOpen;
export const selectCartItemCount = (state: CartState) =>
  state.items.reduce((count, item) => count + item.quantity, 0);

// Convenience hooks
export const useCartItems = () => useCartStore(selectCartItems);
export const useCartTotal = () => useCartStore(selectCartTotal);
export const useCartIsOpen = () => useCartStore(selectCartIsOpen);
export const useCartItemCount = () => useCartStore(selectCartItemCount);

// Actions hooks
export const useCartActions = () => {
  const addItem = useCartStore((state) => state.addItem);
  const removeItem = useCartStore((state) => state.removeItem);
  const updateQuantity = useCartStore((state) => state.updateQuantity);
  const clearCart = useCartStore((state) => state.clearCart);
  const toggleCart = useCartStore((state) => state.toggleCart);
  const setCartOpen = useCartStore((state) => state.setCartOpen);
  const reset = useCartStore((state) => state.reset);

  return {
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    toggleCart,
    setCartOpen,
    reset,
  };
};
