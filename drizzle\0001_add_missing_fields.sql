-- Migration: Add missing fields to existing tables
-- Created: 2025-01-23
-- Description: Adds missing fields that were referenced in the codebase but not present in the database schema

-- Add missing fields to accounts table
ALTER TABLE "accounts" ADD COLUMN "user_id" uuid;
ALTER TABLE "accounts" ADD COLUMN "provider" varchar(50);
ALTER TABLE "accounts" ADD COLUMN "provider_user_id" varchar(255);
ALTER TABLE "accounts" ADD COLUMN "access_token" text;
ALTER TABLE "accounts" ADD COLUMN "refresh_token" text;
ALTER TABLE "accounts" ADD COLUMN "expires_at" timestamp;

-- Add missing fields to orders table
ALTER TABLE "orders" ADD COLUMN "order_no" varchar(100) NOT NULL;
ALTER TABLE "orders" ADD COLUMN "billing_provider" varchar(50) NOT NULL;
ALTER TABLE "orders" ADD COLUMN "user_email" varchar(255) NOT NULL;
ALTER TABLE "orders" ADD COLUMN "user_uuid" uuid NOT NULL;

-- Add missing fields to credit_transactions table
ALTER TABLE "credit_transactions" ADD COLUMN "trans_no" varchar(100) NOT NULL;

-- Add missing fields to notifications table
ALTER TABLE "notifications" ADD COLUMN "account_id" uuid;

-- Add missing fields to subscriptions table
ALTER TABLE "subscriptions" ADD COLUMN "account_id" uuid;
ALTER TABLE "subscriptions" ADD COLUMN "stripe_subscription_id" varchar(255);
ALTER TABLE "subscriptions" ADD COLUMN "stripe_customer_id" varchar(255);

-- Add missing fields to sessions table
ALTER TABLE "sessions" ADD COLUMN "session_token" varchar(255);
ALTER TABLE "sessions" ADD COLUMN "is_active" boolean DEFAULT true;

-- Create unique constraints for new fields
ALTER TABLE "orders" ADD CONSTRAINT "orders_order_no_unique" UNIQUE("order_no");
ALTER TABLE "credit_transactions" ADD CONSTRAINT "credit_transactions_trans_no_unique" UNIQUE("trans_no");

-- Create indexes for better performance
CREATE INDEX "orders_order_no_idx" ON "orders" ("order_no");
CREATE INDEX "orders_user_email_idx" ON "orders" ("user_email");
CREATE INDEX "orders_user_uuid_idx" ON "orders" ("user_uuid");
CREATE INDEX "accounts_user_id_idx" ON "accounts" ("user_id");
CREATE INDEX "accounts_provider_idx" ON "accounts" ("provider");
CREATE INDEX "credit_transactions_trans_no_idx" ON "credit_transactions" ("trans_no");
CREATE INDEX "notifications_account_id_idx" ON "notifications" ("account_id");
CREATE INDEX "subscriptions_account_id_idx" ON "subscriptions" ("account_id");
CREATE INDEX "sessions_session_token_idx" ON "sessions" ("session_token");

-- Add foreign key constraints where appropriate
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE cascade;
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_account_id_accounts_id_fk" FOREIGN KEY ("account_id") REFERENCES "accounts"("id") ON DELETE cascade;
ALTER TABLE "subscriptions" ADD CONSTRAINT "subscriptions_account_id_accounts_id_fk" FOREIGN KEY ("account_id") REFERENCES "accounts"("id") ON DELETE cascade;
