/**
 * 简化的 Google One Tap 测试页面
 * 最小化实现，用于调试基本功能
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { useEffect, useState } from "react";

export const meta: MetaFunction = () => {
  return [
    { title: "Simple Google One Tap Test" },
    { name: "description", content: "Simple test for Google One Tap" },
  ];
};

export async function loader({}: LoaderFunctionArgs) {
  return json({
    googleClientId: process.env.GOOGLE_CLIENT_ID,
  });
}

// 声明 Google API 类型
declare global {
  interface Window {
    google?: {
      accounts?: {
        id?: {
          initialize: (config: any) => void;
          prompt: (callback?: any) => void;
          renderButton: (element: HTMLElement, config: any) => void;
        };
      };
    };
  }
}

export default function SimpleGoogleTest() {
  const { googleClientId } = useLoaderData<typeof loader>();
  const [logs, setLogs] = useState<string[]>([]);
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    setLogs(prev => [...prev, logMessage]);
    console.log(logMessage);
  };

  useEffect(() => {
    if (!googleClientId) {
      addLog("❌ Google Client ID not found");
      return;
    }

    addLog(`✅ Starting with Client ID: ${googleClientId}`);
    addLog(`✅ Current origin: ${window.location.origin}`);

    // 加载 Google GSI 脚本
    const script = document.createElement("script");
    script.src = "https://accounts.google.com/gsi/client";
    script.async = true;
    script.defer = true;

    script.onload = () => {
      addLog("✅ Google GSI script loaded");
      setIsScriptLoaded(true);

      // 等待一下确保 API 可用
      setTimeout(() => {
        if (window.google?.accounts?.id) {
          addLog("✅ Google Accounts API available");
          initializeGoogleOneTap();
        } else {
          addLog("❌ Google Accounts API not available after script load");
        }
      }, 500);
    };

    script.onerror = () => {
      addLog("❌ Failed to load Google GSI script");
    };

    document.head.appendChild(script);

    return () => {
      const existingScript = document.querySelector('script[src="https://accounts.google.com/gsi/client"]');
      if (existingScript) {
        existingScript.remove();
      }
    };
  }, [googleClientId]);

  const initializeGoogleOneTap = () => {
    if (!window.google?.accounts?.id || !googleClientId) {
      addLog("❌ Cannot initialize: API or Client ID missing");
      return;
    }

    try {
      addLog("🔧 Initializing Google One Tap...");

      window.google.accounts.id.initialize({
        client_id: googleClientId,
        callback: (response: any) => {
          addLog("✅ Google callback triggered!");
          addLog(`Credential received: ${response.credential ? 'Yes' : 'No'}`);
          
          if (response.credential) {
            addLog(`Credential length: ${response.credential.length}`);
            // 设置 cookie 并重定向
            document.cookie = `g_credential=${response.credential}; Path=/; SameSite=Lax${window.location.protocol === "https:" ? "; Secure" : ""}`;
            addLog("✅ Cookie set, redirecting...");
            window.location.href = "/auth/google/callback";
          }
        },
        auto_select: false,
        cancel_on_tap_outside: true,
        use_fedcm_for_prompt: false,
      });

      addLog("✅ Google One Tap initialized");

      // 显示提示
      window.google.accounts.id.prompt((notification: any) => {
        addLog("📋 Prompt notification received");
        
        if (notification.isNotDisplayed?.()) {
          const reason = notification.getNotDisplayedReason?.();
          addLog(`❌ One Tap not displayed: ${reason}`);
          
          // 根据错误原因提供建议
          switch (reason) {
            case "unregistered_origin":
              addLog(`💡 解决方案: 在 Google Console 中添加 ${window.location.origin} 到授权的 JavaScript 来源`);
              break;
            case "opt_out_or_no_session":
              addLog("💡 解决方案: 清除浏览器数据或使用无痕模式");
              break;
            case "invalid_client":
              addLog("💡 解决方案: 检查 Google Client ID 配置");
              break;
            default:
              addLog(`💡 未知错误: ${reason}`);
          }
        } else {
          addLog("✅ One Tap prompt displayed successfully!");
        }
      });

    } catch (error) {
      addLog(`❌ Initialization error: ${error}`);
    }
  };

  const handleManualTrigger = () => {
    if (!window.google?.accounts?.id) {
      addLog("❌ Google API not available for manual trigger");
      return;
    }

    addLog("🔄 Manually triggering Google One Tap...");
    window.google.accounts.id.prompt();
  };

  const handleReinitialize = () => {
    addLog("🔄 Reinitializing Google One Tap...");
    if (isScriptLoaded) {
      initializeGoogleOneTap();
    } else {
      addLog("❌ Script not loaded yet");
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">简化 Google One Tap 测试</h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 状态信息 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">状态信息</h2>
            <div className="space-y-2 text-sm">
              <div>
                <strong>Client ID:</strong> 
                <span className={googleClientId ? "text-green-600 ml-2" : "text-red-600 ml-2"}>
                  {googleClientId ? "已配置" : "未配置"}
                </span>
              </div>
              <div>
                <strong>脚本加载:</strong> 
                <span className={isScriptLoaded ? "text-green-600 ml-2" : "text-yellow-600 ml-2"}>
                  {isScriptLoaded ? "已加载" : "加载中..."}
                </span>
              </div>
              <div>
                <strong>当前域名:</strong> 
                <span className="text-blue-600 ml-2">{typeof window !== "undefined" ? window.location.origin : "SSR"}</span>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">操作</h2>
            <div className="space-y-3">
              <button
                onClick={handleManualTrigger}
                disabled={!isScriptLoaded}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
              >
                手动触发 One Tap
              </button>
              
              <button
                onClick={handleReinitialize}
                disabled={!isScriptLoaded}
                className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400"
              >
                重新初始化
              </button>
              
              <button
                onClick={clearLogs}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                清除日志
              </button>
              
              <button
                onClick={() => window.location.href = "/auth/google/oauth"}
                className="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                尝试传统 OAuth
              </button>
            </div>
          </div>

          {/* 日志显示 */}
          <div className="bg-white p-6 rounded-lg shadow lg:col-span-2">
            <h2 className="text-xl font-semibold mb-4">实时日志</h2>
            <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm h-64 overflow-y-auto">
              {logs.length === 0 ? (
                <div className="text-gray-500">等待日志...</div>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">{log}</div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
