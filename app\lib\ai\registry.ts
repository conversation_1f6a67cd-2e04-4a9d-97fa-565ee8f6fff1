// app/lib/ai/registry.ts
// DEPRECATED: This file has been consolidated into ai-providers.ts
// This file is kept for backward compatibility but will be removed in the future.
// Use functions from ai-providers.ts instead.

// Re-export functions from ai-providers.ts for backward compatibility
export {
  AI_PROVIDERS,
  type AIEnvironmentVariables,
  type AIProvider,
  type AIProviderConfig,
  createAIModel,
  getAvailableProviders,
  getProviderModels,
  supportsReasoning,
  validateProviderModel,
} from "./ai-providers";

/**
 * @deprecated Use createAIModel from ai-providers.ts instead
 */
export function getModel(providerId: string, modelId: string, env?: { OPENAI_API_KEY?: string }) {
  console.warn("getModel is deprecated. Use createAIModel from ai-providers.ts instead.");
  throw new Error("Use createAIModel from ai-providers.ts instead of getModel");
}

/**
 * @deprecated Use validateProviderModel from ai-providers.ts instead
 */
export function isModelAvailable(providerId: string, modelId: string, type?: string): boolean {
  console.warn(
    "isModelAvailable is deprecated. Use validateProviderModel from ai-providers.ts instead."
  );
  return false;
}
