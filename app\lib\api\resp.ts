import { json } from "@remix-run/node";

/**
 * Standard API response structure
 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
}

/**
 * Success response with data
 */
export function respData<T>(data: T, message = "success") {
  return json<ApiResponse<T>>({
    code: 0,
    message,
    data,
  });
}

/**
 * Error response
 */
export function respErr(message: string, code = -1) {
  return json<ApiResponse>(
    {
      code,
      message,
    },
    { status: 400 }
  );
}

/**
 * Success response without data
 */
export function respOk(message = "success") {
  return json<ApiResponse>({
    code: 0,
    message,
  });
}

/**
 * Custom response with specific code and message
 */
export function respJson<T>(code: number, message: string, data?: T) {
  const status = code === 0 ? 200 : code === -2 ? 401 : 400;
  return json<ApiResponse<T>>(
    {
      code,
      message,
      data,
    },
    { status }
  );
}

/**
 * Server error response
 */
export function respServerErr(message = "Internal server error") {
  return json<ApiResponse>(
    {
      code: -500,
      message,
    },
    { status: 500 }
  );
}
