/**
 * Advanced Cache Management System
 * Provides multi-layer caching with Redis-like in-memory storage and intelligent cache strategies
 */

export interface CacheConfig {
  defaultTTL: number; // Default time-to-live in seconds
  maxSize: number; // Maximum cache size (number of entries)
  enableCompression: boolean;
  enableMetrics: boolean;
  cleanupInterval: number; // Cleanup interval in milliseconds
}

export interface CacheEntry<T = any> {
  value: T;
  expiresAt: number;
  createdAt: number;
  accessCount: number;
  lastAccessed: number;
  size: number; // Estimated size in bytes
  compressed?: boolean;
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  evictions: number;
  totalSize: number;
  entryCount: number;
  hitRate: number;
  avgAccessTime: number;
}

export interface CacheStrategy {
  name: string;
  shouldCache: (key: string, value: any) => boolean;
  getTTL: (key: string, value: any) => number;
  shouldEvict: (entry: CacheEntry) => boolean;
}

// Default cache configuration
export const DEFAULT_CACHE_CONFIG: CacheConfig = {
  defaultTTL: 300, // 5 minutes
  maxSize: 10000, // 10k entries
  enableCompression: true,
  enableMetrics: true,
  cleanupInterval: 60000, // 1 minute
};

// In-memory cache storage
const cacheStore = new Map<string, CacheEntry>();
const cacheMetrics: CacheMetrics = {
  hits: 0,
  misses: 0,
  sets: 0,
  deletes: 0,
  evictions: 0,
  totalSize: 0,
  entryCount: 0,
  hitRate: 0,
  avgAccessTime: 0,
};

let cleanupInterval: NodeJS.Timeout | null = null;

/**
 * Cache strategies for different data types
 */
export const CACHE_STRATEGIES = {
  // User data - medium TTL, always cache
  user: {
    name: "user",
    shouldCache: () => true,
    getTTL: () => 600, // 10 minutes
    shouldEvict: (entry) => entry.accessCount < 2 && Date.now() - entry.lastAccessed > 300000, // 5 min
  },

  // API responses - short TTL, cache successful responses
  api: {
    name: "api",
    shouldCache: (key, value) => value && !value.error,
    getTTL: () => 180, // 3 minutes
    shouldEvict: (entry) => Date.now() - entry.lastAccessed > 600000, // 10 min
  },

  // Analytics data - long TTL, always cache
  analytics: {
    name: "analytics",
    shouldCache: () => true,
    getTTL: () => 1800, // 30 minutes
    shouldEvict: (entry) => entry.accessCount < 3,
  },

  // Database queries - medium TTL, cache non-empty results
  database: {
    name: "database",
    shouldCache: (key, value) => value && (Array.isArray(value) ? value.length > 0 : true),
    getTTL: () => 300, // 5 minutes
    shouldEvict: (entry) => Date.now() - entry.lastAccessed > 900000, // 15 min
  },

  // Static content - very long TTL, always cache
  static: {
    name: "static",
    shouldCache: () => true,
    getTTL: () => 3600, // 1 hour
    shouldEvict: () => false, // Never evict static content
  },

  // Session data - short TTL, always cache
  session: {
    name: "session",
    shouldCache: () => true,
    getTTL: () => 900, // 15 minutes
    shouldEvict: (entry) => Date.now() - entry.lastAccessed > 1800000, // 30 min
  },
} as const;

/**
 * Initialize cache manager
 */
export function initCacheManager(config: Partial<CacheConfig> = {}): void {
  const finalConfig = { ...DEFAULT_CACHE_CONFIG, ...config };

  if (cleanupInterval) {
    clearInterval(cleanupInterval);
  }

  // Start cleanup interval
  cleanupInterval = setInterval(() => {
    cleanupExpiredEntries();
    enforceMaxSize();
  }, finalConfig.cleanupInterval);

  console.log("[CACHE] Cache manager initialized with config:", finalConfig);
}

/**
 * Get value from cache
 */
export function cacheGet<T = any>(key: string): T | null {
  const startTime = Date.now();
  const entry = cacheStore.get(key);

  if (!entry) {
    cacheMetrics.misses++;
    updateMetrics();
    return null;
  }

  // Check if expired
  if (entry.expiresAt < Date.now()) {
    cacheStore.delete(key);
    cacheMetrics.misses++;
    cacheMetrics.evictions++;
    updateMetrics();
    return null;
  }

  // Update access statistics
  entry.accessCount++;
  entry.lastAccessed = Date.now();
  cacheMetrics.hits++;

  // Update average access time
  const accessTime = Date.now() - startTime;
  cacheMetrics.avgAccessTime = (cacheMetrics.avgAccessTime + accessTime) / 2;

  updateMetrics();

  // Decompress if needed
  if (entry.compressed && typeof entry.value === "string") {
    try {
      return JSON.parse(entry.value) as T;
    } catch {
      return entry.value as T;
    }
  }

  return entry.value as T;
}

/**
 * Set value in cache
 */
export function cacheSet<T = any>(
  key: string,
  value: T,
  ttl?: number,
  strategy?: keyof typeof CACHE_STRATEGIES
): void {
  const cacheStrategy = strategy ? CACHE_STRATEGIES[strategy] : null;

  // Check if should cache
  if (cacheStrategy && !cacheStrategy.shouldCache(key, value)) {
    return;
  }

  // Determine TTL
  const finalTTL = ttl || cacheStrategy?.getTTL(key, value) || DEFAULT_CACHE_CONFIG.defaultTTL;
  const expiresAt = Date.now() + finalTTL * 1000;

  // Estimate size
  const size = estimateSize(value);

  // Compress if enabled and beneficial
  let finalValue = value;
  let compressed = false;
  if (DEFAULT_CACHE_CONFIG.enableCompression && size > 1000) {
    try {
      const stringValue = JSON.stringify(value);
      if (stringValue.length > 500) {
        finalValue = stringValue as T;
        compressed = true;
      }
    } catch {
      // Keep original value if compression fails
    }
  }

  const entry: CacheEntry<T> = {
    value: finalValue,
    expiresAt,
    createdAt: Date.now(),
    accessCount: 0,
    lastAccessed: Date.now(),
    size,
    compressed,
  };

  cacheStore.set(key, entry);
  cacheMetrics.sets++;
  cacheMetrics.totalSize += size;
  cacheMetrics.entryCount++;

  updateMetrics();

  // Enforce max size if needed
  if (cacheStore.size > DEFAULT_CACHE_CONFIG.maxSize) {
    enforceMaxSize();
  }
}

/**
 * Delete value from cache
 */
export function cacheDelete(key: string): boolean {
  const entry = cacheStore.get(key);
  if (entry) {
    cacheStore.delete(key);
    cacheMetrics.deletes++;
    cacheMetrics.totalSize -= entry.size;
    cacheMetrics.entryCount--;
    updateMetrics();
    return true;
  }
  return false;
}

/**
 * Check if key exists in cache
 */
export function cacheHas(key: string): boolean {
  const entry = cacheStore.get(key);
  if (!entry) return false;

  // Check if expired
  if (entry.expiresAt < Date.now()) {
    cacheStore.delete(key);
    cacheMetrics.evictions++;
    updateMetrics();
    return false;
  }

  return true;
}

/**
 * Clear all cache entries
 */
export function cacheClear(): void {
  const entryCount = cacheStore.size;
  cacheStore.clear();
  cacheMetrics.entryCount = 0;
  cacheMetrics.totalSize = 0;
  cacheMetrics.evictions += entryCount;
  updateMetrics();
}

/**
 * Get cache statistics
 */
export function getCacheMetrics(): CacheMetrics {
  return { ...cacheMetrics };
}

/**
 * Get cache entries for debugging
 */
export function getCacheEntries(): Array<{ key: string; entry: CacheEntry }> {
  return Array.from(cacheStore.entries()).map(([key, entry]) => ({ key, entry }));
}

/**
 * Cache with automatic key generation
 */
export async function cacheWrap<T>(
  keyOrFn: string | (() => string),
  fetchFn: () => Promise<T> | T,
  ttl?: number,
  strategy?: keyof typeof CACHE_STRATEGIES
): Promise<T> {
  const key = typeof keyOrFn === "function" ? keyOrFn() : keyOrFn;

  // Try to get from cache first
  const cached = cacheGet<T>(key);
  if (cached !== null) {
    return cached;
  }

  // Fetch and cache
  const value = await fetchFn();
  cacheSet(key, value, ttl, strategy);
  return value;
}

/**
 * Cleanup expired entries
 */
function cleanupExpiredEntries(): void {
  const now = Date.now();
  let cleanedCount = 0;
  let cleanedSize = 0;

  for (const [key, entry] of cacheStore.entries()) {
    if (entry.expiresAt < now) {
      cacheStore.delete(key);
      cleanedCount++;
      cleanedSize += entry.size;
    }
  }

  if (cleanedCount > 0) {
    cacheMetrics.evictions += cleanedCount;
    cacheMetrics.entryCount -= cleanedCount;
    cacheMetrics.totalSize -= cleanedSize;
    updateMetrics();
    console.log(`[CACHE] Cleaned up ${cleanedCount} expired entries (${cleanedSize} bytes)`);
  }
}

/**
 * Enforce maximum cache size using LRU eviction
 */
function enforceMaxSize(): void {
  if (cacheStore.size <= DEFAULT_CACHE_CONFIG.maxSize) {
    return;
  }

  // Sort entries by last accessed time (LRU)
  const entries = Array.from(cacheStore.entries()).sort(
    ([, a], [, b]) => a.lastAccessed - b.lastAccessed
  );

  const toEvict = entries.slice(0, cacheStore.size - DEFAULT_CACHE_CONFIG.maxSize);
  let evictedSize = 0;

  for (const [key, entry] of toEvict) {
    cacheStore.delete(key);
    evictedSize += entry.size;
  }

  cacheMetrics.evictions += toEvict.length;
  cacheMetrics.entryCount -= toEvict.length;
  cacheMetrics.totalSize -= evictedSize;
  updateMetrics();

  if (toEvict.length > 0) {
    console.log(
      `[CACHE] Evicted ${toEvict.length} entries to enforce max size (${evictedSize} bytes)`
    );
  }
}

/**
 * Estimate object size in bytes
 */
function estimateSize(obj: any): number {
  if (obj === null || obj === undefined) return 0;

  if (typeof obj === "string") return obj.length * 2; // UTF-16
  if (typeof obj === "number") return 8;
  if (typeof obj === "boolean") return 4;

  if (Array.isArray(obj)) {
    return obj.reduce((size, item) => size + estimateSize(item), 0);
  }

  if (typeof obj === "object") {
    return Object.entries(obj).reduce(
      (size, [key, value]) => size + estimateSize(key) + estimateSize(value),
      0
    );
  }

  return 100; // Default estimate for unknown types
}

/**
 * Update cache metrics
 */
function updateMetrics(): void {
  const total = cacheMetrics.hits + cacheMetrics.misses;
  cacheMetrics.hitRate = total > 0 ? (cacheMetrics.hits / total) * 100 : 0;
}

/**
 * Shutdown cache manager
 */
export function shutdownCacheManager(): void {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
  }
  cacheClear();
  console.log("[CACHE] Cache manager shutdown");
}

// NOTE: Cache manager initialization is now done lazily to avoid global scope issues in Cloudflare Workers
// Call initCacheManager() manually in your handlers when needed
