import type { EmailTemplate } from "../templating.server";
import { populateTemplate } from "../templating.server";

export interface WelcomeTemplateVariables {
  name: string;
  credits: number;
  inviteBonus?: boolean;
  inviteCode: string;
  dashboardUrl: string;
  docsUrl: string;
  supportUrl: string;
  [key: string]: string | number | boolean | undefined;
}

const subjectTemplate = "🎉 Welcome to AI SaaS Starter, {{name}}!";
const htmlTemplate = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to AI SaaS Starter</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
    .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 30px; text-align: center; }
    .content { padding: 40px 30px; }
    .credits-box { background: #f0f9ff; border: 2px solid #0ea5e9; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center; }
    .button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 10px 5px; }
    .features { margin: 30px 0; }
    .feature { margin: 15px 0; padding: 15px; background: #f8fafc; border-radius: 6px; }
    .footer { background: #f8fafc; padding: 30px; text-align: center; color: #64748b; font-size: 14px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1 style="margin: 0; font-size: 28px;">🎉 Welcome to AI SaaS Starter!</h1>
      <p style="margin: 10px 0 0 0; opacity: 0.9;">Hi {{name}}, you're all set to start building with AI!</p>
    </div>

    <div class="content">
      <h2>🚀 You're Ready to Get Started!</h2>
      <p>Thanks for joining AI SaaS Starter! We've set up your account with everything you need to start building amazing AI-powered applications.</p>

      <div class="credits-box">
        <h3 style="margin: 0 0 10px 0; color: #0ea5e9;">💎 {{credits}} Free Credits Added!</h3>
        <p style="margin: 0; color: #0369a1;">Start experimenting with our AI tools right away - no payment required!</p>
        {{#inviteBonus}}
        <p style="margin: 10px 0 0 0; color: #059669; font-weight: 600;">🎁 Bonus: +50 credits for using invite code!</p>
        {{/inviteBonus}}
      </div>

      <div class="features">
        <h3>🛠️ What You Can Do Now:</h3>
        <div class="feature">
          <strong>🤖 AI Chat Interface</strong> - Start conversations with multiple AI models
        </div>
        <div class="feature">
          <strong>🎨 Image Generation</strong> - Create stunning images with DALL-E and Stable Diffusion
        </div>
        <div class="feature">
          <strong>⚡ Multiple AI Providers</strong> - Access OpenAI, DeepSeek, Cloudflare AI, and more
        </div>
        <div class="feature">
          <strong>📊 Usage Dashboard</strong> - Track your credits and usage in real-time
        </div>
      </div>

      <div style="text-align: center; margin: 30px 0;">
        <a href="{{dashboardUrl}}" class="button">🚀 Start Building Now</a>
        <a href="{{docsUrl}}" class="button" style="background: #64748b;">📚 View Documentation</a>
      </div>

      <h3>🎯 Quick Start Tips:</h3>
      <ol>
        <li><strong>Try the AI Chat:</strong> Go to your dashboard and start a conversation</li>
        <li><strong>Generate Images:</strong> Use the "/image" command in chat</li>
        <li><strong>Check Your Credits:</strong> Monitor usage in your personal dashboard</li>
        <li><strong>Invite Friends:</strong> Share your invite code: <code>{{inviteCode}}</code></li>
      </ol>

      <p>Need help? Just reply to this email or check out our <a href="{{docsUrl}}">documentation</a>.</p>
    </div>

    <div class="footer">
      <p><strong>AI SaaS Starter Team</strong></p>
      <p>Building the future of AI-powered applications</p>
      <p style="margin-top: 20px;">
        <a href="{{dashboardUrl}}" style="color: #667eea;">Dashboard</a> •
        <a href="{{docsUrl}}" style="color: #667eea;">Docs</a> •
        <a href="{{supportUrl}}" style="color: #667eea;">Support</a>
      </p>
    </div>
  </div>
</body>
</html>
`;

const textTemplate = `
🎉 Welcome to AI SaaS Starter, {{name}}!

Hi {{name}}, you're all set to start building with AI!

🚀 YOU'RE READY TO GET STARTED!
Thanks for joining AI SaaS Starter! We've set up your account with everything you need to start building amazing AI-powered applications.

💎 {{credits}} FREE CREDITS ADDED!
Start experimenting with our AI tools right away - no payment required!
{{#inviteBonus}}
🎁 Bonus: +50 credits for using invite code!
{{/inviteBonus}}

🛠️ WHAT YOU CAN DO NOW:
• 🤖 AI Chat Interface - Start conversations with multiple AI models
• 🎨 Image Generation - Create stunning images with DALL-E and Stable Diffusion
• ⚡ Multiple AI Providers - Access OpenAI, DeepSeek, Cloudflare AI, and more
• 📊 Usage Dashboard - Track your credits and usage in real-time

🎯 QUICK START TIPS:
1. Try the AI Chat: Go to your dashboard and start a conversation
2. Generate Images: Use the "/image" command in chat
3. Check Your Credits: Monitor usage in your personal dashboard
4. Invite Friends: Share your invite code: {{inviteCode}}

🚀 Start Building Now: {{dashboardUrl}}
📚 View Documentation: {{docsUrl}}

Need help? Just reply to this email or check out our documentation.

Best regards,
AI SaaS Starter Team
Building the future of AI-powered applications

Dashboard: {{dashboardUrl}}
Docs: {{docsUrl}}
Support: {{supportUrl}}
`;

export const welcomeTemplate: EmailTemplate<WelcomeTemplateVariables> = {
  subject: (vars) => populateTemplate(subjectTemplate, vars),
  html: (vars) => populateTemplate(htmlTemplate, vars),
  text: (vars) => populateTemplate(textTemplate, vars),
};
