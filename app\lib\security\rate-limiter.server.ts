/**
 * Rate Limiting System
 * Implements sliding window rate limiting with Redis-like in-memory storage
 */

import type { Database } from "~/lib/db/db";

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (request: Request, userUuid?: string) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  message?: string;
  standardHeaders?: boolean; // Add rate limit headers to response
  legacyHeaders?: boolean; // Add legacy rate limit headers
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  totalHits: number;
  retryAfter?: number; // Seconds until next request allowed
}

export interface RateLimitEntry {
  count: number;
  resetTime: number;
  requests: number[]; // Timestamps of requests for sliding window
}

// In-memory storage for rate limiting (use Redis in production)
const rateLimitStore = new Map<string, RateLimitEntry>();

// Cleanup interval for expired entries
let cleanupInterval: NodeJS.Timeout | null = null;

/**
 * Default rate limit configurations for different endpoints
 */
export const RATE_LIMIT_CONFIGS = {
  // General API endpoints
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    message: "Too many API requests, please try again later.",
  },

  // Authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // Strict limit for auth attempts
    message: "Too many authentication attempts, please try again later.",
  },

  // AI generation endpoints
  aiGeneration: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 AI requests per minute
    message: "Too many AI generation requests, please try again later.",
  },

  // File upload endpoints
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // 5 uploads per minute
    message: "Too many upload requests, please try again later.",
  },

  // Password reset endpoints
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3, // 3 password reset attempts per hour
    message: "Too many password reset attempts, please try again later.",
  },

  // Email sending endpoints
  email: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10, // 10 emails per hour
    message: "Too many email requests, please try again later.",
  },

  // Admin endpoints
  admin: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 50, // 50 admin requests per 5 minutes
    message: "Too many admin requests, please try again later.",
  },
} as const;

/**
 * Default key generator - uses IP address and optional user UUID
 */
export function defaultKeyGenerator(request: Request, userUuid?: string): string {
  const ip = getClientIP(request);
  const url = new URL(request.url);
  const endpoint = url.pathname;

  if (userUuid) {
    return `rate_limit:${endpoint}:user:${userUuid}`;
  }

  return `rate_limit:${endpoint}:ip:${ip}`;
}

/**
 * Get client IP address from request
 */
function getClientIP(request: Request): string {
  const headers = request.headers;

  const ipHeaders = ["cf-connecting-ip", "x-forwarded-for", "x-real-ip", "x-client-ip"];

  for (const header of ipHeaders) {
    const value = headers.get(header);
    if (value) {
      return value.split(",")[0].trim();
    }
  }

  return "unknown";
}

/**
 * Initialize rate limiter cleanup
 */
export function initRateLimiter(): void {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
  }

  // Clean up expired entries every 5 minutes
  cleanupInterval = setInterval(
    () => {
      cleanupExpiredEntries();
    },
    5 * 60 * 1000
  );
}

/**
 * Cleanup expired rate limit entries
 */
function cleanupExpiredEntries(): void {
  const now = Date.now();
  const keysToDelete: string[] = [];

  for (const [key, entry] of rateLimitStore.entries()) {
    if (entry.resetTime < now) {
      keysToDelete.push(key);
    }
  }

  keysToDelete.forEach((key) => rateLimitStore.delete(key));

  if (keysToDelete.length > 0) {
    console.log(`[RATE_LIMITER] Cleaned up ${keysToDelete.length} expired entries`);
  }
}

/**
 * Check rate limit for a request
 */
export async function checkRateLimit(
  request: Request,
  config: RateLimitConfig,
  userUuid?: string
): Promise<RateLimitResult> {
  const keyGenerator = config.keyGenerator || defaultKeyGenerator;
  const key = keyGenerator(request, userUuid);
  const now = Date.now();
  const windowStart = now - config.windowMs;

  // Get or create rate limit entry
  let entry = rateLimitStore.get(key);
  if (!entry) {
    entry = {
      count: 0,
      resetTime: now + config.windowMs,
      requests: [],
    };
    rateLimitStore.set(key, entry);
  }

  // Clean up old requests (sliding window)
  entry.requests = entry.requests.filter((timestamp) => timestamp > windowStart);

  // Update count
  entry.count = entry.requests.length;

  // Check if limit exceeded
  const allowed = entry.count < config.maxRequests;

  if (allowed) {
    // Add current request timestamp
    entry.requests.push(now);
    entry.count = entry.requests.length;
  }

  // Update reset time if needed
  if (entry.resetTime < now) {
    entry.resetTime = now + config.windowMs;
  }

  const remaining = Math.max(0, config.maxRequests - entry.count);
  const resetTime = new Date(entry.resetTime);

  const result: RateLimitResult = {
    allowed,
    remaining,
    resetTime,
    totalHits: entry.count,
  };

  if (!allowed) {
    result.retryAfter = Math.ceil((entry.resetTime - now) / 1000);
  }

  return result;
}

/**
 * Apply rate limiting to a request
 */
export async function applyRateLimit(
  request: Request,
  config: RateLimitConfig,
  userUuid?: string
): Promise<{ allowed: boolean; response?: Response }> {
  try {
    const result = await checkRateLimit(request, config, userUuid);

    if (!result.allowed) {
      const headers = new Headers();

      // Add standard rate limit headers
      if (config.standardHeaders !== false) {
        headers.set("RateLimit-Limit", config.maxRequests.toString());
        headers.set("RateLimit-Remaining", result.remaining.toString());
        headers.set("RateLimit-Reset", result.resetTime.getTime().toString());
      }

      // Add legacy headers
      if (config.legacyHeaders) {
        headers.set("X-RateLimit-Limit", config.maxRequests.toString());
        headers.set("X-RateLimit-Remaining", result.remaining.toString());
        headers.set("X-RateLimit-Reset", result.resetTime.getTime().toString());
      }

      if (result.retryAfter) {
        headers.set("Retry-After", result.retryAfter.toString());
      }

      headers.set("Content-Type", "application/json");

      const response = new Response(
        JSON.stringify({
          error: "Rate limit exceeded",
          message: config.message || "Too many requests, please try again later.",
          retryAfter: result.retryAfter,
          resetTime: result.resetTime.toISOString(),
        }),
        {
          status: 429,
          headers,
        }
      );

      return { allowed: false, response };
    }

    return { allowed: true };
  } catch (error) {
    console.error("Rate limiting error:", error);
    // Fail open - allow request if rate limiting fails
    return { allowed: true };
  }
}

/**
 * Get rate limit status for a key
 */
export async function getRateLimitStatus(
  request: Request,
  config: RateLimitConfig,
  userUuid?: string
): Promise<RateLimitResult> {
  return checkRateLimit(request, config, userUuid);
}

/**
 * Reset rate limit for a key
 */
export async function resetRateLimit(
  request: Request,
  config: RateLimitConfig,
  userUuid?: string
): Promise<void> {
  const keyGenerator = config.keyGenerator || defaultKeyGenerator;
  const key = keyGenerator(request, userUuid);
  rateLimitStore.delete(key);
}

/**
 * Get all rate limit entries (for debugging)
 */
export function getAllRateLimitEntries(): Map<string, RateLimitEntry> {
  return new Map(rateLimitStore);
}

/**
 * Clear all rate limit entries
 */
export function clearAllRateLimits(): void {
  rateLimitStore.clear();
}

/**
 * Rate limiting middleware wrapper
 */
export function withRateLimit(config: RateLimitConfig) {
  return async (
    request: Request,
    userUuid?: string
  ): Promise<{ allowed: boolean; response?: Response }> => {
    return applyRateLimit(request, config, userUuid);
  };
}

/**
 * Create rate limit middleware for specific endpoint types
 */
export const rateLimiters = {
  api: withRateLimit(RATE_LIMIT_CONFIGS.api),
  auth: withRateLimit(RATE_LIMIT_CONFIGS.auth),
  aiGeneration: withRateLimit(RATE_LIMIT_CONFIGS.aiGeneration),
  upload: withRateLimit(RATE_LIMIT_CONFIGS.upload),
  passwordReset: withRateLimit(RATE_LIMIT_CONFIGS.passwordReset),
  email: withRateLimit(RATE_LIMIT_CONFIGS.email),
  admin: withRateLimit(RATE_LIMIT_CONFIGS.admin),
};

/**
 * Shutdown rate limiter
 */
export function shutdownRateLimiter(): void {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
  }
  rateLimitStore.clear();
}

// NOTE: Rate limiter initialization is now done lazily to avoid global scope issues in Cloudflare Workers
// Call initRateLimiter() manually in your handlers when needed
