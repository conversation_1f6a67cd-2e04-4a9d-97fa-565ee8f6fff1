import { useSearchParams } from "@remix-run/react";
import { useCallback, useEffect, useState } from "react";
import type { AIResponse, Conversation, ConversationResponse, Message } from "./types";
import { isAIResponse, isConversationResponse, isValidMessage } from "./types";

interface UseChatOptions {
  conversationId?: string;
  onError?: (error: string) => void;
}

interface UseChatReturn {
  messages: Message[];
  conversation: Conversation | null;
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;
  sendMessage: (content: string) => Promise<void>;
  refreshConversation: () => Promise<void>;
  clearError: () => void;
}

export function useChat({ conversationId, onError }: UseChatOptions = {}): UseChatReturn {
  const [searchParams, setSearchParams] = useSearchParams();
  const [messages, setMessages] = useState<Message[]>([]);
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const handleError = useCallback(
    (errorMessage: string) => {
      setError(errorMessage);
      onError?.(errorMessage);
    },
    [onError]
  );

  // Load conversation and messages
  const loadConversation = useCallback(
    async (id: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/chat/conversations/${id}`);
        const data = await response.json();

        if (!isConversationResponse(data)) {
          throw new Error("Invalid response format");
        }

        if (data.code !== 200) {
          throw new Error(data.error || "Failed to load conversation");
        }

        if (data.data) {
          setConversation(data.data.conversation);

          // Validate and set messages
          const validMessages = data.data.messages.filter(isValidMessage);
          setMessages(validMessages);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to load conversation";
        handleError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [handleError]
  );

  // Refresh current conversation
  const refreshConversation = useCallback(async () => {
    const currentId = conversationId || searchParams.get("conversation");
    if (currentId) {
      await loadConversation(currentId);
    }
  }, [conversationId, searchParams, loadConversation]);

  // Send message
  const sendMessage = useCallback(
    async (content: string) => {
      if (!content.trim() || isLoading || isStreaming) return;

      const currentConversationId = conversationId || searchParams.get("conversation");
      setIsStreaming(true);
      setError(null);

      // Add user message optimistically
      const userMessage: Message = {
        id: `temp-${Date.now()}`,
        role: "user",
        content: content.trim(),
        createdAt: new Date().toISOString(),
      };

      setMessages((prev) => [...prev, userMessage]);

      try {
        const requestBody = {
          message: content.trim(),
          conversationId: currentConversationId,
        };

        const response = await fetch("/api/ai/stream-text", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        });

        const data = await response.json();

        if (!isAIResponse(data)) {
          throw new Error("Invalid response format");
        }

        if (data.code !== 200) {
          throw new Error(data.error || "Failed to send message");
        }

        // Handle the response
        if (data.data) {
          let responseText = "";

          if (typeof data.data.result === "string") {
            responseText = data.data.result;
          } else if (data.data.result?.response) {
            responseText = data.data.result.response;
          } else if (data.data.text) {
            responseText = data.data.text;
          }

          if (responseText) {
            const assistantMessage: Message = {
              id: `response-${Date.now()}`,
              role: "assistant",
              content: responseText,
              createdAt: new Date().toISOString(),
              model: data.data.model,
            };

            setMessages((prev) => [...prev, assistantMessage]);

            // Update URL if this is a new conversation
            if (
              !currentConversationId &&
              searchParams.get("conversation") !== assistantMessage.id
            ) {
              setSearchParams({ conversation: assistantMessage.id });
            }
          }
        }
      } catch (err) {
        // Remove the optimistic user message on error
        setMessages((prev) => prev.filter((msg) => msg.id !== userMessage.id));

        const errorMessage = err instanceof Error ? err.message : "Failed to send message";
        handleError(errorMessage);
      } finally {
        setIsStreaming(false);
      }
    },
    [conversationId, searchParams, isLoading, isStreaming, setSearchParams, handleError]
  );

  // Load conversation on mount or when ID changes
  useEffect(() => {
    const currentId = conversationId || searchParams.get("conversation");
    if (currentId) {
      loadConversation(currentId);
    } else {
      // Clear state for new conversation
      setMessages([]);
      setConversation(null);
      setError(null);
    }
  }, [conversationId, searchParams, loadConversation]);

  return {
    messages,
    conversation,
    isLoading,
    isStreaming,
    error,
    sendMessage,
    refreshConversation,
    clearError,
  };
}
