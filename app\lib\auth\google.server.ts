/**
 * Google Authentication Handler
 * Handles Google One Tap ID Token verification and user creation
 */

import { eq } from "drizzle-orm";
import { createDbFromEnv } from "~/lib/db";
import { sessions, users } from "~/lib/db/schema";
import { processNewUserOnboarding } from "~/services/onboarding.server";
import { getUuid } from "./hash";
import { createTokenPair, getUserAgent, getUserIP } from "./jwt.server";

// Google ID Token payload interface
export interface GoogleIdTokenPayload {
  iss: string; // Issuer
  aud: string; // Audience (your Google Client ID)
  sub: string; // Subject (Google user ID)
  email: string;
  email_verified: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  iat: number; // Issued at
  exp: number; // Expires at
}

/**
 * Verify Google ID Token
 */
export async function verifyGoogleIdToken(
  idToken: string,
  clientId: string
): Promise<GoogleIdTokenPayload | null> {
  try {
    // Use Google's tokeninfo endpoint to verify the token
    const response = await fetch(`https://oauth2.googleapis.com/tokeninfo?id_token=${idToken}`);

    if (!response.ok) {
      console.error("Google token verification failed:", response.status);
      return null;
    }

    const payload = (await response.json()) as GoogleIdTokenPayload;

    // Verify the token is for our application
    if (payload.aud !== clientId) {
      console.error("Token audience mismatch");
      return null;
    }

    // Verify the token hasn't expired
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp < now) {
      console.error("Token has expired");
      return null;
    }

    // Verify email is verified
    if (!payload.email_verified) {
      console.error("Email not verified");
      return null;
    }

    return payload;
  } catch (error) {
    console.error("Error verifying Google ID token:", error);
    return null;
  }
}

/**
 * Handle Google authentication
 * Creates or updates user and creates session
 */
export async function handleGoogleAuth(
  idToken: string,
  request: Request,
  env?: Record<string, string | undefined>
): Promise<{
  success: boolean;
  user?: any;
  accessToken?: string;
  refreshToken?: string;
  error?: string;
}> {
  try {
    const clientId = env?.GOOGLE_CLIENT_ID || process.env.GOOGLE_CLIENT_ID;
    if (!clientId) {
      return {
        success: false,
        error: "Google Client ID not configured",
      };
    }

    // Verify the Google ID token
    const googlePayload = await verifyGoogleIdToken(idToken, clientId);
    if (!googlePayload) {
      return {
        success: false,
        error: "Invalid Google ID token",
      };
    }

    const db = createDbFromEnv(env);

    // Check if user already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, googlePayload.email))
      .limit(1);

    let user;

    if (existingUser.length > 0) {
      // Update existing user
      user = existingUser[0];

      // Update user info from Google
      await db
        .update(users)
        .set({
          name: googlePayload.name,
          avatar: googlePayload.picture,
          signinType: "oauth",
          signinProvider: "google",
          signinOpenid: googlePayload.sub,
          signinIp: getUserIP(request),
          updatedAt: new Date(),
        })
        .where(eq(users.id, user.id));
    } else {
      // Create new user
      const newUserData = {
        id: getUuid(),
        uuid: getUuid(),
        name: googlePayload.name,
        email: googlePayload.email,
        avatar: googlePayload.picture,
        credits: 100, // Give new users some initial credits
        signinType: "oauth",
        signinProvider: "google",
        signinOpenid: googlePayload.sub,
        signinIp: getUserIP(request),
        isAffiliate: false,
      };

      const insertResult = await db.insert(users).values(newUserData).returning();

      if (insertResult.length === 0) {
        return {
          success: false,
          error: "Failed to create user",
        };
      }

      user = insertResult[0];

      // Process new user onboarding
      try {
        await processNewUserOnboarding(
          {
            id: user.id,
            uuid: user.uuid,
            name: user.name,
            email: user.email,
            inviteCode: user.inviteCode,
            invitedBy: user.invitedBy,
          },
          db,
          env
        );
      } catch (error) {
        console.error("Onboarding process failed:", error);
        // Continue with authentication even if onboarding fails
      }
    }

    // Create session
    const sessionData = {
      id: getUuid(),
      userId: user.id,
      sessionToken: getUuid(),
      refreshToken: getUuid(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      refreshExpiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      userAgent: getUserAgent(request),
      ipAddress: getUserIP(request),
      isActive: true,
    };

    const sessionResult = await db.insert(sessions).values(sessionData).returning();

    if (sessionResult.length === 0) {
      return {
        success: false,
        error: "Failed to create session",
      };
    }

    const session = sessionResult[0];

    // Create JWT tokens
    const tokens = await createTokenPair(
      {
        id: user.id,
        uuid: user.uuid,
        email: user.email,
        name: user.name,
        avatar: user.avatar || undefined,
      },
      session.id
    );

    // Return relevant user information
    return {
      success: true,
      user: {
        id: user.id,
        uuid: user.uuid,
        email: user.email,
        name: user.name,
        avatar: user.avatar,
        credits: user.credits,
      },
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
    };
  } catch (error) {
    console.error("Google authentication error:", error);
    return {
      success: false,
      error: "Authentication failed",
    };
  }
}

/**
 * Generate invite code for new users
 */
function generateInviteCode(): string {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Handle user registration with invite code
 */
export async function handleUserRegistration(
  googlePayload: GoogleIdTokenPayload,
  inviteCode?: string,
  env?: Record<string, string | undefined>
): Promise<any> {
  const db = createDbFromEnv(env);

  let invitedBy = null;
  let bonusCredits = 0;

  // Check if invite code is valid
  if (inviteCode) {
    const inviter = await db.select().from(users).where(eq(users.inviteCode, inviteCode)).limit(1);

    if (inviter.length > 0) {
      invitedBy = inviter[0].uuid;
      bonusCredits = 50; // Bonus credits for using invite code
    }
  }

  const newUserData = {
    id: getUuid(),
    uuid: getUuid(),
    name: googlePayload.name,
    email: googlePayload.email,
    avatar: googlePayload.picture,
    credits: 100 + bonusCredits, // Base credits + invite bonus
    inviteCode: generateInviteCode(),
    invitedBy,
    theme: "system", // Default theme
    signinType: "oauth",
    signinProvider: "google",
    signinOpenid: googlePayload.sub,
    isAffiliate: false,
  };

  const insertResult = await db.insert(users).values(newUserData).returning();

  return insertResult[0];
}

/**
 * Exchange authorization code for tokens (for server-side OAuth flow)
 * This uses the Client Secret and is more secure than One Tap for some use cases
 */
export async function exchangeCodeForTokens(
  code: string,
  clientId: string,
  clientSecret: string,
  redirectUri: string
): Promise<{
  access_token?: string;
  id_token?: string;
  refresh_token?: string;
  error?: string;
}> {
  const maxRetries = 3;
  const timeoutMs = 10000; // 10 seconds timeout

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Token exchange attempt ${attempt}/${maxRetries}`);

      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

      const response = await fetch("https://oauth2.googleapis.com/token", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          code,
          client_id: clientId,
          client_secret: clientSecret,
          redirect_uri: redirectUri,
          grant_type: "authorization_code",
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Token exchange failed (attempt ${attempt}):`, response.status, errorText);

        // Don't retry for client errors (4xx)
        if (response.status >= 400 && response.status < 500) {
          return { error: `Token exchange failed: ${response.status}` };
        }

        // Retry for server errors (5xx) or network issues
        if (attempt === maxRetries) {
          return { error: "Token exchange failed after multiple attempts" };
        }

        // Wait before retry (exponential backoff)
        await new Promise((resolve) => setTimeout(resolve, 2 ** attempt * 1000));
        continue;
      }

      const tokens = await response.json();
      console.log("Token exchange successful");
      return tokens;
    } catch (error) {
      console.error(`Token exchange error (attempt ${attempt}):`, error);

      // Check if it's a timeout or network error
      const isNetworkError =
        error instanceof Error &&
        (error.name === "AbortError" ||
          error.message.includes("fetch failed") ||
          error.message.includes("ConnectTimeoutError") ||
          error.message.includes("ECONNRESET") ||
          error.message.includes("ENOTFOUND"));

      if (isNetworkError && attempt < maxRetries) {
        console.log(`Network error detected, retrying in ${2 ** attempt} seconds...`);
        await new Promise((resolve) => setTimeout(resolve, 2 ** attempt * 1000));
        continue;
      }

      // If it's the last attempt or not a network error, return error
      if (attempt === maxRetries) {
        return {
          error: `Token exchange error after ${maxRetries} attempts: ${error instanceof Error ? error.message : "Unknown error"}`,
        };
      }
    }
  }

  return { error: "Token exchange failed: Maximum retries exceeded" };
}

/**
 * Refresh access token using refresh token
 */
export async function refreshAccessToken(
  refreshToken: string,
  clientId: string,
  clientSecret: string
): Promise<{
  access_token?: string;
  id_token?: string;
  error?: string;
}> {
  try {
    const response = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        refresh_token: refreshToken,
        client_id: clientId,
        client_secret: clientSecret,
        grant_type: "refresh_token",
      }),
    });

    if (!response.ok) {
      console.error("Token refresh failed:", response.status);
      return { error: "Token refresh failed" };
    }

    const tokens = await response.json();
    return tokens;
  } catch (error) {
    console.error("Token refresh error:", error);
    return { error: "Token refresh error" };
  }
}
