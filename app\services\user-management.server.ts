/**
 * Unified User Management Service
 * Consolidates user authentication, credit management, and onboarding functionality
 */

import { eq, sql } from "drizzle-orm";
import type { Database } from "~/lib/db/db";
import { creditTransactions, users } from "~/lib/db/schema";
import { emailService } from "~/lib/email/service.server";
import { welcomeTemplate } from "~/lib/email/templates/welcome";
import { updateUserCredits } from "~/models/user";

// =============================================================================
// TYPES AND ENUMS
// =============================================================================

export enum CreditsTransType {
  Ping = "ping",
  Purchase = "purchase",
  Reward = "reward",
  Refund = "refund",
  InviteBonus = "invite_bonus",
  Referral = "referral",
  AITextGeneration = "ai_text_generation",
  AIImageGeneration = "ai_image_generation",
  AIStreamText = "ai_stream_text",
  AIEmbedding = "ai_embedding",
  AITextClassification = "ai_text_classification",
  AIImageClassification = "ai_image_classification",
  AISpeechToText = "ai_speech_to_text",
}

export enum CreditsAmount {
  PingCost = 1,
  DefaultReward = 10,
  AITextGenerationCost = 5,
  AIImageGenerationCost = 10,
  AIStreamTextCost = 3,
  AIEmbeddingCost = 2,
  AITextClassificationCost = 1,
  AIImageClassificationCost = 3,
  AISpeechToTextCost = 4,
}

export interface CreditTransaction {
  user_uuid: string;
  trans_type: CreditsTransType;
  credits: number;
  description?: string;
}

export interface OnboardingData {
  user: {
    id: string;
    uuid: string;
    name: string;
    email: string;
    credits: number;
    inviteCode?: string;
  };
  isNewUser: boolean;
  inviteBonus?: boolean;
  invitedBy?: string;
}

export interface WelcomeEmailData {
  name: string;
  email: string;
  credits: number;
  inviteCode?: string;
  inviteBonus?: boolean;
  dashboardUrl: string;
  docsUrl: string;
  supportUrl: string;
}

// =============================================================================
// AUTHENTICATION FUNCTIONS
// =============================================================================

/**
 * Get user UUID from session/auth
 * This is a placeholder implementation - you'll need to integrate with your auth system
 */
export async function getUserUuid(): Promise<string | null> {
  // TODO: Implement actual user authentication logic
  // This could be from:
  // - Session cookies
  // - JWT tokens
  // - Auth0/Clerk/Supabase auth
  // - Custom authentication

  // For now, return a mock user UUID for testing (valid UUID format)
  // Remove this and implement real auth
  return "550e8400-e29b-41d4-a716-************";
}

/**
 * Get user email from session/auth
 */
export async function getUserEmail(): Promise<string | null> {
  // TODO: Implement actual user email retrieval
  // This should get the email from the same auth source as getUserUuid

  // Mock implementation
  return "<EMAIL>";
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(): Promise<boolean> {
  const userUuid = await getUserUuid();
  return userUuid !== null;
}

/**
 * Get user from request headers/cookies
 * This function should extract user info from the request
 */
export async function getUserFromRequest(request: Request): Promise<{
  uuid: string | null;
  email: string | null;
}> {
  // TODO: Implement request-based user extraction
  // This could involve:
  // - Reading cookies
  // - Parsing Authorization headers
  // - Validating JWT tokens

  // Mock implementation
  return {
    uuid: await getUserUuid(),
    email: await getUserEmail(),
  };
}

// =============================================================================
// CREDIT MANAGEMENT FUNCTIONS
// =============================================================================

/**
 * Generate unique transaction number
 */
function generateTransactionNumber(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `TXN-${timestamp}-${random}`.toUpperCase();
}

/**
 * Get user's current credits
 */
export async function getUserCredits(userUuid: string, db: Database): Promise<number> {
  try {
    const result = await db
      .select({ credits: users.credits })
      .from(users)
      .where(eq(users.uuid, userUuid))
      .limit(1);

    if (result.length === 0) {
      return 0;
    }

    return result[0].credits || 0;
  } catch (error) {
    console.error("Error getting user credits:", error);
    return 0;
  }
}

/**
 * Decrease user credits
 */
export async function decreaseCredits(
  transaction: CreditTransaction,
  db: Database
): Promise<boolean> {
  try {
    // Start a transaction to ensure atomicity
    const currentCredits = await getUserCredits(transaction.user_uuid, db);

    if (currentCredits < transaction.credits) {
      console.error(
        `Insufficient credits: user has ${currentCredits}, needs ${transaction.credits}`
      );
      return false;
    }

    const newCredits = currentCredits - transaction.credits;

    // Update user credits
    const updateSuccess = await updateUserCredits(transaction.user_uuid, newCredits, db);
    if (!updateSuccess) {
      return false;
    }

    // Log the transaction
    await db.insert(creditTransactions).values({
      transNo: generateTransactionNumber(),
      userUuid: transaction.user_uuid,
      transType: transaction.trans_type,
      credits: -transaction.credits, // Negative for decrease
      description: transaction.description || `Credits used for ${transaction.trans_type}`,
      createdAt: new Date(),
    });

    console.log(
      `Decreased ${transaction.credits} credits for user ${transaction.user_uuid} (${transaction.trans_type})`
    );
    return true;
  } catch (error) {
    console.error("Error decreasing credits:", error);
    return false;
  }
}

/**
 * Increase user credits
 */
export async function increaseCredits(
  transaction: CreditTransaction,
  db: Database
): Promise<boolean> {
  try {
    const currentCredits = await getUserCredits(transaction.user_uuid, db);
    const newCredits = currentCredits + transaction.credits;

    // Update user credits
    const updateSuccess = await updateUserCredits(transaction.user_uuid, newCredits, db);
    if (!updateSuccess) {
      return false;
    }

    // Log the transaction
    await db.insert(creditTransactions).values({
      transNo: generateTransactionNumber(),
      userUuid: transaction.user_uuid,
      transType: transaction.trans_type,
      credits: transaction.credits, // Positive for increase
      description: transaction.description || `Credits added for ${transaction.trans_type}`,
      createdAt: new Date(),
    });

    console.log(
      `Increased ${transaction.credits} credits for user ${transaction.user_uuid} (${transaction.trans_type})`
    );
    return true;
  } catch (error) {
    console.error("Error increasing credits:", error);
    return false;
  }
}

/**
 * Check if user has enough credits
 */
export async function hasEnoughCredits(
  userUuid: string,
  requiredCredits: number,
  db: Database
): Promise<boolean> {
  const currentCredits = await getUserCredits(userUuid, db);
  return currentCredits >= requiredCredits;
}

/**
 * Get credit transaction history for a user
 */
export async function getCreditTransactionHistory(
  userUuid: string,
  db: Database,
  limit = 50
): Promise<any[]> {
  try {
    const result = await db
      .select()
      .from(creditTransactions)
      .where(eq(creditTransactions.userUuid, userUuid))
      .orderBy(sql`${creditTransactions.createdAt} DESC`)
      .limit(limit);

    return result.map((transaction) => ({
      id: transaction.id,
      user_uuid: transaction.userUuid,
      trans_type: transaction.transType,
      credits: transaction.credits,
      description: transaction.description,
      created_at: transaction.createdAt.toISOString(),
    }));
  } catch (error) {
    console.error("Error getting credit transaction history:", error);
    return [];
  }
}

// =============================================================================
// ONBOARDING FUNCTIONS
// =============================================================================

/**
 * Send welcome email to new user
 */
export async function sendWelcomeEmail(
  emailData: WelcomeEmailData,
  env?: Record<string, string | undefined>
): Promise<{ success: boolean; error?: string }> {
  try {
    const templateVariables = {
      name: emailData.name,
      credits: emailData.credits.toString(),
      inviteCode: emailData.inviteCode || "",
      inviteBonus: emailData.inviteBonus ? "true" : "",
      dashboardUrl: emailData.dashboardUrl,
      docsUrl: emailData.docsUrl,
      supportUrl: emailData.supportUrl,
    };

    const template = welcomeTemplate(templateVariables);

    const result = await emailService.sendEmail({
      to: { email: emailData.email, name: emailData.name },
      from: { email: "<EMAIL>", name: "Your App" }, // TODO: Configure from env
      subject: template.subject,
      html: template.html,
      text: template.text,
      tags: [
        { name: "EmailType", value: "Welcome" },
        { name: "UserType", value: emailData.inviteBonus ? "Invited" : "Direct" },
      ],
    });

    if (result.success) {
      console.log(`Welcome email sent to ${emailData.email}`);
      return { success: true };
    } else {
      console.error("Failed to send welcome email:", result.error);
      return { success: false, error: result.error };
    }
  } catch (error) {
    console.error("Error sending welcome email:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to send welcome email",
    };
  }
}

/**
 * Process invite code and award bonus credits
 */
export async function processInviteCode(
  userUuid: string,
  inviteCode: string,
  db: Database
): Promise<{ success: boolean; bonusCredits?: number; error?: string }> {
  try {
    // Find the user who owns this invite code
    const inviter = await db
      .select({ uuid: users.uuid, name: users.name })
      .from(users)
      .where(eq(users.inviteCode, inviteCode))
      .limit(1);

    if (inviter.length === 0) {
      return { success: false, error: "Invalid invite code" };
    }

    const inviterUuid = inviter[0].uuid;

    // Don't allow users to invite themselves
    if (inviterUuid === userUuid) {
      return { success: false, error: "Cannot use your own invite code" };
    }

    const bonusCredits = 50; // Configurable bonus amount

    // Award bonus credits to the new user
    const newUserBonus = await increaseCredits(
      {
        user_uuid: userUuid,
        trans_type: CreditsTransType.InviteBonus,
        credits: bonusCredits,
        description: `Invite bonus from ${inviter[0].name}`,
      },
      db
    );

    // Award referral credits to the inviter
    const inviterBonus = await increaseCredits(
      {
        user_uuid: inviterUuid,
        trans_type: CreditsTransType.Referral,
        credits: bonusCredits,
        description: "Referral bonus for successful invite",
      },
      db
    );

    if (newUserBonus && inviterBonus) {
      console.log(
        `Processed invite code ${inviteCode}: ${bonusCredits} credits awarded to both users`
      );
      return { success: true, bonusCredits };
    } else {
      return { success: false, error: "Failed to award bonus credits" };
    }
  } catch (error) {
    console.error("Error processing invite code:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to process invite code",
    };
  }
}

/**
 * Complete user onboarding process
 */
export async function completeOnboarding(
  onboardingData: OnboardingData,
  db: Database,
  env?: Record<string, string | undefined>
): Promise<{ success: boolean; error?: string }> {
  try {
    const { user, isNewUser, inviteBonus, invitedBy } = onboardingData;

    if (!isNewUser) {
      return { success: true }; // Nothing to do for existing users
    }

    // Award initial signup credits
    const initialCredits = 20; // Configurable initial amount
    const creditsAwarded = await increaseCredits(
      {
        user_uuid: user.uuid,
        trans_type: CreditsTransType.Reward,
        credits: initialCredits,
        description: "Welcome bonus for new account",
      },
      db
    );

    if (!creditsAwarded) {
      console.error("Failed to award initial credits to new user");
    }

    // Process invite code if provided
    let inviteBonusCredits = 0;
    if (invitedBy) {
      const inviteResult = await processInviteCode(user.uuid, invitedBy, db);
      if (inviteResult.success) {
        inviteBonusCredits = inviteResult.bonusCredits || 0;
      }
    }

    // Mark user as onboarded
    await db
      .update(users)
      .set({
        onboardingCompleted: true,
        updatedAt: new Date(),
      })
      .where(eq(users.uuid, user.uuid));

    // Send welcome email
    const totalCredits = user.credits + initialCredits + inviteBonusCredits;
    const emailResult = await sendWelcomeEmail(
      {
        name: user.name,
        email: user.email,
        credits: totalCredits,
        inviteCode: user.inviteCode,
        inviteBonus: !!inviteBonus,
        dashboardUrl: env?.WEB_URL ? `${env.WEB_URL}/console/dashboard` : "/console/dashboard",
        docsUrl: env?.WEB_URL ? `${env.WEB_URL}/docs` : "/docs",
        supportUrl: env?.WEB_URL ? `${env.WEB_URL}/contact` : "/contact",
      },
      env
    );

    if (!emailResult.success) {
      console.warn("Onboarding completed but welcome email failed:", emailResult.error);
    }

    console.log(`Onboarding completed for user ${user.uuid} (${user.email})`);
    return { success: true };
  } catch (error) {
    console.error("Error completing onboarding:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to complete onboarding",
    };
  }
}

/**
 * Generate unique invite code for user
 */
export async function generateInviteCode(
  userUuid: string,
  db: Database
): Promise<{ success: boolean; inviteCode?: string; error?: string }> {
  try {
    let attempts = 0;
    let inviteCode: string;

    do {
      // Generate a random invite code
      inviteCode = Math.random().toString(36).substring(2, 8).toUpperCase();
      attempts++;

      // Check if this code already exists
      const existing = await db
        .select({ uuid: users.uuid })
        .from(users)
        .where(eq(users.inviteCode, inviteCode))
        .limit(1);

      if (existing.length === 0) {
        break; // Code is unique
      }

      if (attempts > 10) {
        return { success: false, error: "Failed to generate unique invite code" };
      }
    } while (true);

    // Update user with the new invite code
    await db
      .update(users)
      .set({
        inviteCode,
        updatedAt: new Date(),
      })
      .where(eq(users.uuid, userUuid));

    console.log(`Generated invite code ${inviteCode} for user ${userUuid}`);
    return { success: true, inviteCode };
  } catch (error) {
    console.error("Error generating invite code:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to generate invite code",
    };
  }
}

// =============================================================================
// EXPORTS FOR BACKWARD COMPATIBILITY
// =============================================================================

// Re-export credit-related functions for backward compatibility
export {
  decreaseCredits as decreaseUserCredits,
  increaseCredits as increaseUserCredits,
  getUserCredits as getCurrentUserCredits,
  hasEnoughCredits as checkUserCredits,
  getCreditTransactionHistory as getUserCreditHistory,
};
