import type { ReactNode } from "react";

export interface NavItem {
  title: string;
  url: string;
  description?: string;
  icon?: ReactNode;
  children?: NavItem[];
  target?: string;
}

export interface SearchProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  showSearchButton?: boolean;
  className?: string;
}

export interface UserAccountProps {
  user?: {
    name: string;
    email: string;
    avatar?: string;
    initials?: string;
  };
  onSignIn?: () => void;
  onSignOut?: () => void;
  showNotifications?: boolean;
  notificationCount?: number;
}

export interface TrustSignalsProps {
  phone?: string;
  certifications?: string[];
  securityBadge?: boolean;
}

export interface BrandProps {
  title: string;
  url: string;
  logo?: string;
}

export interface ActionButton {
  title: string;
  url: string;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  target?: string;
}

export interface HeaderProps {
  brand?: BrandProps;
  navigation?: NavItem[];
  buttons?: ActionButton[];
  search?: SearchProps;
  userAccount?: UserAccountProps;
  trustSignals?: TrustSignalsProps;
}
