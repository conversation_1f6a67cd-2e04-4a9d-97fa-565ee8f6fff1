/**
 * Google Console 配置检查页面
 * 帮助诊断 Google One Tap 配置问题
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { useEffect, useState } from "react";

export const meta: MetaFunction = () => {
  return [
    { title: "Google Console Configuration Check" },
    { name: "description", content: "Check Google Console configuration for One Tap" },
  ];
};

export async function loader({}: LoaderFunctionArgs) {
  return json({
    googleClientId: process.env.GOOGLE_CLIENT_ID,
    nodeEnv: process.env.NODE_ENV,
    appUrl: process.env.APP_URL,
  });
}

export default function GoogleConfigCheck() {
  const { googleClientId, nodeEnv, appUrl } = useLoaderData<typeof loader>();
  const [currentOrigin, setCurrentOrigin] = useState<string>("");
  const [configStatus, setConfigStatus] = useState<{
    clientIdValid: boolean;
    originMatch: boolean;
    suggestions: string[];
  }>({
    clientIdValid: false,
    originMatch: false,
    suggestions: [],
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      const origin = window.location.origin;
      setCurrentOrigin(origin);

      // Check configuration
      const suggestions: string[] = [];
      const clientIdValid = googleClientId?.endsWith(".apps.googleusercontent.com") || false;
      const originMatch = appUrl === origin;

      if (!clientIdValid) {
        suggestions.push("Google Client ID 格式不正确，应该以 .apps.googleusercontent.com 结尾");
      }

      if (!originMatch) {
        suggestions.push(`环境变量 APP_URL (${appUrl}) 与当前域名 (${origin}) 不匹配`);
      }

      suggestions.push("确保在 Google Console 中添加以下授权的 JavaScript 来源:");
      suggestions.push(`- ${origin}`);
      suggestions.push("- http://localhost:5173");
      suggestions.push("- http://localhost:3000");

      suggestions.push("确保在 Google Console 中添加以下授权的重定向 URI:");
      suggestions.push(`- ${origin}/auth/google/callback`);
      suggestions.push(`- ${origin}/auth/google/oauth`);

      setConfigStatus({
        clientIdValid,
        originMatch,
        suggestions,
      });
    }
  }, [googleClientId, appUrl]);

  const handleOpenGoogleConsole = () => {
    window.open("https://console.developers.google.com/apis/credentials", "_blank");
  };

  const handleTestTokenInfo = async () => {
    if (!googleClientId) return;

    try {
      const response = await fetch(
        `https://oauth2.googleapis.com/tokeninfo?client_id=${googleClientId}`
      );
      const data = await response.text();
      
      console.log("Token info response:", response.status, data);
      
      if (response.status === 400) {
        alert("✅ Client ID 格式正确（400 错误是正常的，因为缺少 token）");
      } else {
        alert(`⚠️ 意外的响应状态: ${response.status}\n${data}`);
      }
    } catch (error) {
      console.error("Token info test failed:", error);
      alert(`❌ 测试失败: ${error}`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Google Console 配置检查</h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 当前配置状态 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">当前配置状态</h2>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <span className={configStatus.clientIdValid ? "text-green-600" : "text-red-600"}>
                  {configStatus.clientIdValid ? "✅" : "❌"}
                </span>
                <span>Client ID 格式</span>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={configStatus.originMatch ? "text-green-600" : "text-yellow-600"}>
                  {configStatus.originMatch ? "✅" : "⚠️"}
                </span>
                <span>域名配置匹配</span>
              </div>

              <div className="mt-4 p-3 bg-gray-50 rounded">
                <p className="text-sm font-medium">当前信息:</p>
                <p className="text-xs text-gray-600">Client ID: {googleClientId || "未配置"}</p>
                <p className="text-xs text-gray-600">当前域名: {currentOrigin}</p>
                <p className="text-xs text-gray-600">环境变量 APP_URL: {appUrl}</p>
                <p className="text-xs text-gray-600">环境: {nodeEnv}</p>
              </div>
            </div>
          </div>

          {/* 配置建议 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">配置建议</h2>
            <div className="space-y-2">
              {configStatus.suggestions.map((suggestion, index) => (
                <div key={index} className="text-sm">
                  {suggestion.startsWith("-") ? (
                    <div className="ml-4 text-blue-600 font-mono">{suggestion}</div>
                  ) : (
                    <div className="text-gray-700">{suggestion}</div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="bg-white p-6 rounded-lg shadow lg:col-span-2">
            <h2 className="text-xl font-semibold mb-4">快速操作</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={handleOpenGoogleConsole}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                打开 Google Console
              </button>
              
              <button
                onClick={handleTestTokenInfo}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                测试 Client ID
              </button>
              
              <button
                onClick={() => window.location.href = "/test/google-auth"}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                转到调试页面
              </button>
            </div>
          </div>

          {/* 详细配置步骤 */}
          <div className="bg-white p-6 rounded-lg shadow lg:col-span-2">
            <h2 className="text-xl font-semibold mb-4">详细配置步骤</h2>
            <div className="space-y-4 text-sm">
              <div>
                <h3 className="font-medium text-gray-900">1. 打开 Google Console</h3>
                <p className="text-gray-600">访问 https://console.developers.google.com/apis/credentials</p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900">2. 选择你的项目</h3>
                <p className="text-gray-600">确保选择了正确的 Google Cloud 项目</p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900">3. 编辑 OAuth 2.0 客户端 ID</h3>
                <p className="text-gray-600">找到你的客户端 ID 并点击编辑</p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900">4. 添加授权的 JavaScript 来源</h3>
                <div className="ml-4 space-y-1 font-mono text-blue-600">
                  <div>http://localhost:5173</div>
                  <div>http://localhost:3000</div>
                  <div>{currentOrigin && currentOrigin}</div>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900">5. 添加授权的重定向 URI</h3>
                <div className="ml-4 space-y-1 font-mono text-blue-600">
                  <div>{currentOrigin}/auth/google/callback</div>
                  <div>{currentOrigin}/auth/google/oauth</div>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900">6. 保存并等待生效</h3>
                <p className="text-gray-600">配置更改可能需要几分钟到几小时才能生效</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
