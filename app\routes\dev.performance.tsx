/**
 * Performance monitoring and analytics demo page
 */

import type { MetaFunction } from "@remix-run/node";
import { Activity, BarChart3, Image, Zap } from "lucide-react";
import { PerformanceDashboard } from "~/components/dev/performance-dashboard";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
// Removed optimized-image import for Cloudflare Workers compatibility

export const meta: MetaFunction = () => {
  return [
    { title: "Performance & Analytics Demo | Remix SaaS Starter" },
    {
      name: "description",
      content:
        "Demonstration of performance monitoring, analytics tracking, and image optimization features",
    },
    {
      name: "keywords",
      content:
        "Performance, Analytics, Google Analytics, Web Vitals, Image Optimization, Cloudflare",
    },
  ];
};

export default function PerformancePage() {
  return (
    <UnifiedLayout
      hero={{
        title: "Performance & Analytics",
        description:
          "Comprehensive performance monitoring and analytics demonstration. Track Core Web Vitals, optimize images, and analyze bundle performance.",
      }}
    >
      <section className="py-16">
        <div className="max-w-6xl mx-auto px-4 space-y-12">
          {/* Performance Dashboard */}
          <section className="mb-12">
            <div className="flex items-center space-x-2 mb-6">
              <Activity className="h-6 w-6 text-blue-600" />
              <h2 className="text-2xl font-semibold">Performance Metrics</h2>
            </div>
            <PerformanceDashboard />
          </section>

          {/* Analytics Demo */}
          <section className="mb-12">
            <div className="flex items-center space-x-2 mb-6">
              <BarChart3 className="h-6 w-6 text-green-600" />
              <h2 className="text-2xl font-semibold">Analytics Tracking</h2>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Analytics Features</CardTitle>
                  <CardDescription>Enhanced Google Analytics implementation</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">✅ Implemented Features:</h4>
                    <ul className="text-sm space-y-1 text-gray-600">
                      <li>• Core Web Vitals tracking</li>
                      <li>• Enhanced event tracking</li>
                      <li>• Performance monitoring</li>
                      <li>• GDPR consent management</li>
                      <li>• Custom parameters support</li>
                      <li>• Error tracking</li>
                      <li>• Form submission tracking</li>
                      <li>• Language change tracking</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">🔧 Configuration:</h4>
                    <ul className="text-sm space-y-1 text-gray-600">
                      <li>• Production-only tracking</li>
                      <li>• Debug mode for development</li>
                      <li>• Automatic page view tracking</li>
                      <li>• Custom dimension mapping</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Image Optimization Demo */}
          <section className="mb-12">
            <div className="flex items-center space-x-2 mb-6">
              <Image className="h-6 w-6 text-purple-600" />
              <h2 className="text-2xl font-semibold">Image Optimization</h2>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Optimized Images</CardTitle>
                  <CardDescription>Cloudflare-optimized image components</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Hero Image Example */}
                  <div>
                    <h4 className="font-medium mb-2">Hero Image (Priority Loading)</h4>
                    <img
                      src="/logo-light.png"
                      alt="Hero image example"
                      width={400}
                      height={200}
                      className="rounded-lg border"
                    />
                  </div>

                  {/* Avatar Example */}
                  <div>
                    <h4 className="font-medium mb-2">Avatar with Fallback</h4>
                    <div className="flex space-x-2">
                      <img
                        src="/logo-light.png"
                        alt="User avatar"
                        width={48}
                        height={48}
                        className="rounded-full"
                      />
                      <img
                        src="/logo-light.png"
                        alt="Fallback avatar"
                        width={48}
                        height={48}
                        className="rounded-full"
                      />
                    </div>
                  </div>

                  {/* Lazy Loading Example */}
                  <div>
                    <h4 className="font-medium mb-2">Lazy Loaded Image</h4>
                    <img
                      src="/logo-light.png"
                      alt="Lazy loaded image"
                      width={300}
                      height={150}
                      className="rounded border"
                      loading="lazy"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Optimization Features</CardTitle>
                  <CardDescription>Advanced image optimization capabilities</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">🖼️ Image Features:</h4>
                    <ul className="text-sm space-y-1 text-gray-600">
                      <li>• Cloudflare Images integration</li>
                      <li>• Automatic format optimization (WebP/AVIF)</li>
                      <li>• Responsive image generation</li>
                      <li>• Lazy loading with intersection observer</li>
                      <li>• Performance tracking</li>
                      <li>• Fallback image support</li>
                      <li>• Priority loading for above-fold images</li>
                      <li>• Custom compression settings</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">⚡ Performance Benefits:</h4>
                    <ul className="text-sm space-y-1 text-gray-600">
                      <li>• Reduced bandwidth usage</li>
                      <li>• Faster page load times</li>
                      <li>• Better Core Web Vitals scores</li>
                      <li>• Improved user experience</li>
                      <li>• SEO benefits</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Bundle Analysis */}
          <section className="mb-12">
            <div className="flex items-center space-x-2 mb-6">
              <Zap className="h-6 w-6 text-orange-600" />
              <h2 className="text-2xl font-semibold">Bundle Analysis</h2>
            </div>
            <Card>
              <CardHeader>
                <CardTitle>Bundle Optimization</CardTitle>
                <CardDescription>Tools and techniques for optimizing bundle size</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <h4 className="font-medium">📦 Bundle Features:</h4>
                    <ul className="text-sm space-y-1 text-gray-600">
                      <li>• Vite bundle analyzer integration</li>
                      <li>• Manual chunk splitting</li>
                      <li>• Vendor code separation</li>
                      <li>• Tree shaking optimization</li>
                      <li>• Source map generation</li>
                      <li>• Performance budgets</li>
                      <li>• Bundle size monitoring</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">🚀 Usage Commands:</h4>
                    <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                      <div>pnpm run analyze</div>
                      <div className="text-gray-600"># Build with bundle analysis</div>
                      <br />
                      <div>pnpm run perf</div>
                      <div className="text-gray-600"># Build and start for performance testing</div>
                    </div>
                  </div>
                </div>

                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">💡 Optimization Tips:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Use dynamic imports for route-based code splitting</li>
                    <li>• Optimize images and use modern formats</li>
                    <li>• Remove unused dependencies and code</li>
                    <li>• Use CDN for static assets</li>
                    <li>• Enable compression (gzip/brotli)</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </section>

          {/* Footer */}
          <div className="text-center text-gray-600 dark:text-gray-400 text-sm">
            <p>Performance monitoring powered by Google Analytics and Cloudflare</p>
          </div>
        </div>
      </section>
    </UnifiedLayout>
  );
}
