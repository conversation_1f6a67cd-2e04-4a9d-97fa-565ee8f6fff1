export default function HeroBg() {
  return (
    <div className="absolute inset-0 -z-10 overflow-hidden">
      {/* Simple gradient background */}
      <div className="absolute inset-0 bg-gradient-to-b from-muted/30 to-background" />

      {/* Minimal decorative elements */}
      <div className="absolute top-1/4 right-0 w-px h-24 bg-border opacity-50" />
      <div className="absolute bottom-1/4 left-0 w-px h-24 bg-border opacity-50" />

      {/* Subtle accent */}
      <div className="absolute top-0 left-1/2 -translate-x-1/2 w-96 h-px bg-gradient-to-r from-transparent via-border to-transparent opacity-30" />
    </div>
  );
}
