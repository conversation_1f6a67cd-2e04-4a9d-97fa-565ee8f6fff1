// app/lib/ai/types.ts
// AI related type definitions

export interface AiRequestOptions {
  model?: string;
  provider?: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  [key: string]: unknown;
}

export interface AiTextGenerationOptions extends AiRequestOptions {
  prompt: string;
  systemMessage?: string;
  maxTokens?: number;
  temperature?: number;
}

export interface AiImageGenerationOptions extends AiRequestOptions {
  prompt: string;
  size?: "256x256" | "512x512" | "1024x1024" | "1792x1024" | "1024x1792";
  quality?: "standard" | "hd";
  style?: "vivid" | "natural";
  n?: number;
}

export interface KlingProviderConfig {
  accessKey: string;
  secretKey: string;
  videoApiUrl?: string;
  imageApiUrl?: string;
}

export interface AiProvider {
  id: string;
  name: string;
  textModels?: string[];
  imageModels?: string[];
  videoModels?: string[];
}

export interface AiProviderResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
    details?: Record<string, unknown>;
  };
  provider?: string;
  model?: string;
}

// Cloudflare AI specific types
export interface CloudflareAiOptions {
  prompt: string;
  model?: string;
  stream?: boolean;
}

// Test runner types
export interface AiTestCase {
  id: string;
  name: string;
  provider: string;
  model: string;
  prompt: string;
  expectedType?: "text" | "image" | "error";
  maxRetries?: number;
}

export interface AiTestResult {
  testId: string;
  success: boolean;
  response?: unknown;
  error?: string;
  duration: number;
  provider: string;
  model: string;
}
