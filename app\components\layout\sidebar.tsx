import { Link, useLocation, useSearchParams } from "@remix-run/react";
import {
  ChevronDown,
  ChevronRight,
  MessageSquare,
  Plus,
  Settings,
  Sparkles,
  Trash2,
  X,
} from "lucide-react";
import { type ReactNode, useEffect, useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { defaultSidebarConfig } from "~/config/sidebar.config";
import { cn } from "~/lib/utils/utils";

export interface SidebarItem {
  title: string;
  url: string;
  icon: ReactNode;
  badge?: string;
  children?: SidebarItem[];
  description?: string;
}

export interface ChatConversation {
  id: string;
  title: string;
  model?: string;
  provider?: string;
  isArchived: boolean;
  lastMessageAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SidebarProps {
  items?: SidebarItem[];
  className?: string;
  onClose?: () => void;
  showCloseButton?: boolean;
  title?: string;
  logo?: ReactNode;
  // Chat-specific props
  mode?: "navigation" | "chat";
  conversations?: ChatConversation[];
  currentConversationId?: string;
  onNewChat?: () => void;
  onSelectConversation?: (id: string) => void;
  onArchiveConversation?: (id: string) => void;
  isLoadingConversations?: boolean;
  authError?: boolean;
  user?: any;
}

export default function Sidebar({
  items = defaultSidebarConfig,
  className = "",
  onClose,
  showCloseButton = false,
  title = "Navigation",
  logo,
  mode = "navigation",
  conversations = [],
  currentConversationId,
  onNewChat,
  onSelectConversation,
  onArchiveConversation,
  isLoadingConversations = false,
  authError = false,
  user,
}: SidebarProps) {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const location = useLocation();

  const isActive = (url: string) => {
    if (url === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(url);
  };

  const toggleExpanded = (title: string) => {
    setExpandedItems((prev) =>
      prev.includes(title) ? prev.filter((item) => item !== title) : [...prev, title]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: "short" });
    } else {
      return date.toLocaleDateString([], { month: "short", day: "numeric" });
    }
  };

  const handleArchiveConversation = (conversationId: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onArchiveConversation?.(conversationId);
  };

  const renderSidebarItem = (item: SidebarItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.title);
    const active = isActive(item.url);

    return (
      <div key={item.title} className="w-full">
        <div
          className={cn(
            "flex items-center justify-between w-full rounded-lg transition-colors group",
            level === 0 ? "px-3 py-2" : "px-5 py-1.5",
            active
              ? "bg-muted text-foreground"
              : "hover:bg-muted/60 text-muted-foreground hover:text-foreground"
          )}
        >
          <Link to={item.url} className="flex items-center gap-3 flex-1 min-w-0" onClick={onClose}>
            <div
              className={cn(
                "flex-shrink-0 transition-colors",
                active ? "text-foreground" : "text-muted-foreground group-hover:text-foreground"
              )}
            >
              {item.icon}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <span
                  className={cn(
                    "font-medium truncate",
                    level === 0 ? "text-sm" : "text-xs",
                    active ? "text-foreground" : ""
                  )}
                >
                  {item.title}
                </span>
                {item.badge && (
                  <Badge
                    variant="outline"
                    className="text-xs px-2 py-0.5 font-normal border-border/60"
                  >
                    {item.badge}
                  </Badge>
                )}
              </div>
              {item.description && level === 0 && (
                <p className="text-xs text-muted-foreground truncate mt-0.5">{item.description}</p>
              )}
            </div>
          </Link>
          {hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 flex-shrink-0"
              onClick={() => toggleExpanded(item.title)}
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children?.map((child) => renderSidebarItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (mode === "chat") {
    return (
      <div
        className={cn("flex flex-col h-full bg-background border-r border-border/40", className)}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-border/40">
          <div className="flex items-center gap-3">
            <div className="w-7 h-7 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">RS</span>
            </div>
            <span className="font-semibold text-base text-foreground">{title}</span>
          </div>
          {showCloseButton && onClose && (
            <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* New Chat Button */}
        <div className="p-3">
          <Button
            onClick={onNewChat}
            disabled={authError || !user}
            className="w-full justify-start gap-2 bg-primary hover:bg-primary/90 text-primary-foreground border-0 shadow-none font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            size="sm"
          >
            <Plus className="h-4 w-4" />
            New Chat
          </Button>
        </div>

        {/* Conversations List */}
        <div className="flex-1 overflow-y-auto">
          {authError || !user ? (
            <div className="p-4 text-center text-muted-foreground">
              <MessageSquare className="h-6 w-6 mx-auto mb-3 opacity-40" />
              <p className="text-sm font-medium">Please sign in</p>
              <p className="text-xs mt-1 opacity-70">Sign in to access your conversations</p>
              <Link
                to="/auth/login"
                className="inline-block mt-3 px-3 py-1.5 text-xs bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
              >
                Sign In
              </Link>
            </div>
          ) : isLoadingConversations ? (
            <div className="p-4 text-center text-muted-foreground">
              <div className="text-sm">Loading conversations...</div>
            </div>
          ) : conversations.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              <MessageSquare className="h-6 w-6 mx-auto mb-3 opacity-40" />
              <p className="text-sm font-medium">No conversations yet</p>
              <p className="text-xs mt-1 opacity-70">Start a new chat to begin</p>
            </div>
          ) : (
            <div className="px-2 pb-2">
              {conversations.map((conversation) => (
                <div
                  key={conversation.id}
                  className={cn(
                    "group relative flex items-start gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-colors mb-1",
                    currentConversationId === conversation.id
                      ? "bg-muted text-foreground"
                      : "hover:bg-muted/60 text-muted-foreground hover:text-foreground"
                  )}
                  onClick={() => onSelectConversation?.(conversation.id)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      onSelectConversation?.(conversation.id);
                    }
                  }}
                  role="button"
                  tabIndex={0}
                  aria-label={`Select conversation: ${conversation.title}`}
                >
                  <MessageSquare className="h-4 w-4 flex-shrink-0 mt-0.5" />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                      <p className="font-medium text-sm truncate leading-5">{conversation.title}</p>
                      <span className="text-xs text-muted-foreground flex-shrink-0">
                        {formatDate(conversation.lastMessageAt || conversation.createdAt)}
                      </span>
                    </div>
                    {conversation.provider && (
                      <div className="flex items-center gap-1.5 mt-1">
                        <Badge
                          variant="outline"
                          className="text-xs px-2 py-0.5 font-normal border-border/60"
                        >
                          {conversation.provider}
                        </Badge>
                        {conversation.model && (
                          <span className="text-xs text-muted-foreground font-mono">
                            {conversation.model}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-destructive/10 hover:text-destructive"
                    onClick={(e) => handleArchiveConversation(conversation.id, e)}
                  >
                    <Trash2 className="h-3.5 w-3.5" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-3 border-t border-border/40">
          <Link
            to="/console/settings"
            className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-muted/60 text-muted-foreground hover:text-foreground transition-colors"
            onClick={onClose}
          >
            <Settings className="h-4 w-4" />
            <span className="font-medium text-sm">Settings</span>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-background border-r border-border/40", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-border/40">
        <div className="flex items-center gap-3">
          {logo || (
            <div className="w-7 h-7 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">RS</span>
            </div>
          )}
          <span className="font-semibold text-base text-foreground">{title}</span>
        </div>
        {showCloseButton && onClose && (
          <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 overflow-y-auto p-3">
        <div className="space-y-1">{items.map((item) => renderSidebarItem(item))}</div>
      </nav>

      {/* Footer */}
      <div className="p-3 border-t border-border/40">
        <Link
          to="/console/settings"
          className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-muted/60 text-muted-foreground hover:text-foreground transition-colors"
          onClick={onClose}
        >
          <Settings className="h-4 w-4" />
          <span className="font-medium text-sm">Settings</span>
        </Link>
      </div>
    </div>
  );
}
