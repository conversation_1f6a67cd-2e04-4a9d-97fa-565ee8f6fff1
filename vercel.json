{"buildCommand": "pnpm run build", "devCommand": "pnpm run dev", "installCommand": "pnpm install", "framework": "remix", "env": {"NODE_ENV": "production"}, "regions": ["iad1"], "functions": {"app/entry.server.tsx": {"maxDuration": 30}}, "rewrites": [{"source": "/build/(.*)", "destination": "/build/$1"}], "headers": [{"source": "/build/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}]}