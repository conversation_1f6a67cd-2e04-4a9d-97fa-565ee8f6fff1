/* Clean, Minimalist Design System for US/Japanese Users */
:root {
  /* Light theme colors - Ultra clean and minimal */
  --background: 0 0% 100%;
  --foreground: 0 0% 9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 9%;

  /* Primary: Clean blue - professional and trustworthy */
  --primary: 210 100% 50%;
  --primary-foreground: 0 0% 100%;

  /* Secondary: Neutral grays */
  --secondary: 0 0% 96%;
  --secondary-foreground: 0 0% 9%;

  /* Muted: Subtle backgrounds */
  --muted: 0 0% 96%;
  --muted-foreground: 0 0% 45%;

  /* Accent: Interactive elements */
  --accent: 0 0% 96%;
  --accent-foreground: 0 0% 9%;

  /* Destructive: Clear error color */
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;

  /* Borders and inputs: Clean lines */
  --border: 0 0% 90%;
  --input: 0 0% 90%;
  --ring: 210 100% 50%;

  /* Border radius: Subtle rounding */
  --radius: 0.5rem;

  /* Success and warning */
  --success: 142 76% 36%;
  --success-foreground: 0 0% 100%;
  --warning: 38 92% 50%;
  --warning-foreground: 0 0% 100%;
}

.dark {
  /* Dark theme colors - Clean and professional */
  --background: 0 0% 3.9%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;

  /* Primary: Clean blue for dark mode */
  --primary: 210 100% 60%;
  --primary-foreground: 0 0% 9%;

  /* Secondary: Dark neutrals */
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;

  /* Muted: Subtle dark backgrounds */
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;

  /* Accent: Interactive dark elements */
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;

  /* Destructive: Error color for dark mode */
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;

  /* Borders and inputs: Clean dark lines */
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 210 100% 60%;

  /* Success and warning for dark mode */
  --success: 142 76% 36%;
  --success-foreground: 0 0% 98%;
  --warning: 38 92% 50%;
  --warning-foreground: 0 0% 98%;
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease, box-shadow 0.2s ease;
}

/* Ensure proper contrast for text */
.light {
  color-scheme: light;
}

.dark {
  color-scheme: dark;
}

/* Base styles for American-friendly design */
body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Clean Typography Scale */
.text-display {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.text-headline {
  font-size: 2rem;
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.015em;
}

.text-title {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
}

.text-body {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
}

.text-caption {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.4;
}

/* Clean Button Styles */
.btn-primary-gradient {
  background: hsl(var(--primary));
  transition: all 0.2s ease;
}

.btn-primary-gradient:hover {
  background: hsl(var(--primary) / 0.9);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.15);
}

/* Clean Card Styles */
.card-modern {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  box-shadow: 0 1px 3px hsl(0 0% 0% / 0.05);
  transition: all 0.2s ease;
}

.card-modern:hover {
  box-shadow: 0 4px 12px hsl(0 0% 0% / 0.08);
  transform: translateY(-1px);
}

/* Note: .glass class definition moved to animations.css to avoid duplication */

/* Focus styles for accessibility */
.focus-ring {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Clean Background Styles */
.bg-gradient-light {
  background: hsl(var(--background));
}

.bg-gradient-primary {
  background: hsl(var(--primary) / 0.02);
}