/**
 * Admin Monitoring Dashboard
 * Comprehensive system monitoring, logging, and alerting interface
 */

import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Activity,
  AlertCircle,
  AlertTriangle,
  BarChart3,
  Bell,
  BellOff,
  CheckCircle,
  Clock,
  Database,
  Download,
  Eye,
  FileText,
  Filter,
  Globe,
  Info,
  LineChart,
  PieChart,
  RefreshCw,
  Search,
  Server,
  Settings,
  TrendingDown,
  TrendingUp,
  XCircle,
  Zap,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";

// Mock data functions (in production, these would import from actual monitoring modules)
const getCurrentMetrics = () => ({
  timestamp: new Date(),
  system: {
    uptime: 86400, // 24 hours
    memory: { used: 134217728, total: 268435456, percentage: 50.0 },
    cpu: { usage: 25.5 },
  },
  application: {
    responseTime: 245,
    requestCount: 5670,
    errorRate: 2.1,
    activeConnections: 12,
  },
  database: {
    connectionCount: 8,
    queryTime: 19.5,
    slowQueries: 3,
    connectionErrors: 1,
  },
  cache: {
    hitRate: 87.4,
    memoryUsage: 52428800,
    entryCount: 1430,
    evictions: 12,
  },
  external: {
    aiProviders: {
      openai: { status: "healthy", responseTime: 1100, errorRate: 3.2, lastCheck: new Date() },
      anthropic: { status: "healthy", responseTime: 1350, errorRate: 2.8, lastCheck: new Date() },
      cloudflare: { status: "degraded", responseTime: 2100, errorRate: 8.5, lastCheck: new Date() },
    },
    thirdPartyServices: {
      stripe: { status: "healthy", responseTime: 450, lastCheck: new Date() },
      sendgrid: { status: "healthy", responseTime: 320, lastCheck: new Date() },
    },
  },
});

const getActiveAlerts = () => [
  {
    id: "alert_1",
    type: "warning",
    title: "High Response Time",
    message: "Average response time is 1850ms (threshold: 1500ms)",
    timestamp: new Date(Date.now() - 300000), // 5 minutes ago
    resolved: false,
  },
  {
    id: "alert_2",
    type: "critical",
    title: "AI Provider Degraded",
    message: "Cloudflare AI provider is experiencing high error rates (8.5%)",
    timestamp: new Date(Date.now() - 600000), // 10 minutes ago
    resolved: false,
  },
];

const getErrorSummary = () => ({
  totalErrors: 156,
  errorsByLevel: { error: 45, warning: 78, info: 234, debug: 567 },
  errorsByComponent: {
    "ai-service": 23,
    database: 12,
    auth: 8,
    api: 15,
    cache: 3,
  },
  errorRate: 2.1,
  recentErrors: [
    {
      id: "err_1",
      timestamp: new Date(Date.now() - 120000),
      level: "error",
      message: "Database connection timeout",
      component: "database",
      occurrenceCount: 3,
    },
    {
      id: "err_2",
      timestamp: new Date(Date.now() - 180000),
      level: "warning",
      message: "High memory usage detected",
      component: "system",
      occurrenceCount: 1,
    },
    {
      id: "err_3",
      timestamp: new Date(Date.now() - 240000),
      level: "error",
      message: "AI provider rate limit exceeded",
      component: "ai-service",
      occurrenceCount: 5,
    },
  ],
});

const getLogMetrics = () => ({
  totalLogs: 12450,
  logsByLevel: { error: 156, warning: 234, info: 8900, debug: 3160 },
  logsBySource: {
    application: 7800,
    security: 1200,
    performance: 890,
    database: 2560,
  },
  errorRate: 1.25,
  timeline: Array.from({ length: 24 }, (_, i) => ({
    timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000),
    count: Math.floor(Math.random() * 500) + 200,
    errorCount: Math.floor(Math.random() * 20) + 5,
  })),
});

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // TODO: Add admin authentication check

    // Get monitoring data
    const systemMetrics = getCurrentMetrics();
    const activeAlerts = getActiveAlerts();
    const errorSummary = getErrorSummary();
    const logMetrics = getLogMetrics();

    // Calculate system health
    const systemHealth = {
      status: activeAlerts.some((a) => a.type === "critical")
        ? "critical"
        : activeAlerts.some((a) => a.type === "warning")
          ? "warning"
          : "healthy",
      score: Math.max(0, 100 - activeAlerts.length * 10 - errorSummary.errorRate * 5),
      uptime: systemMetrics.system.uptime,
      lastCheck: new Date(),
    };

    return json({
      success: true,
      data: {
        systemMetrics,
        systemHealth,
        activeAlerts,
        errorSummary,
        logMetrics,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Error loading monitoring dashboard:", error);
    return json({ success: false, error: "Failed to load monitoring dashboard" }, { status: 500 });
  }
}

export default function AdminMonitoringPage() {
  const { data } = useLoaderData<typeof loader>();
  const [selectedTab, setSelectedTab] = useState("overview");

  const { systemMetrics, systemHealth, activeAlerts, errorSummary, logMetrics } = data;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "warning":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "critical":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "degraded":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case "warning":
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case "critical":
        return <XCircle className="w-5 h-5 text-red-500" />;
      case "degraded":
        return <AlertCircle className="w-5 h-5 text-orange-500" />;
      default:
        return <Activity className="w-5 h-5 text-gray-500" />;
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "critical":
        return <XCircle className="w-4 h-4 text-red-500" />;
      case "warning":
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case "info":
        return <Info className="w-4 h-4 text-blue-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / k ** i).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                System Monitoring
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Real-time system monitoring, logging, and alerting dashboard
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Logs
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>

        {/* System Health Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Activity className="w-8 h-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    System Health
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {systemHealth.score}%
                  </p>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(systemHealth.status)}
                    <p className="text-xs text-gray-500 capitalize">{systemHealth.status}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="w-8 h-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Uptime</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatUptime(systemMetrics.system.uptime)}
                  </p>
                  <p className="text-xs text-green-600">Since last restart</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="w-8 h-8 text-red-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Active Alerts
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {activeAlerts.length}
                  </p>
                  <p className="text-xs text-red-600">
                    {activeAlerts.filter((a) => a.type === "critical").length} critical
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="w-8 h-8 text-orange-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Error Rate</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {errorSummary.errorRate.toFixed(1)}%
                  </p>
                  <p className="text-xs text-orange-600">{errorSummary.totalErrors} total errors</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Monitoring Information */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="system">System</TabsTrigger>
            <TabsTrigger value="alerts">Alerts</TabsTrigger>
            <TabsTrigger value="errors">Errors</TabsTrigger>
            <TabsTrigger value="logs">Logs</TabsTrigger>
            <TabsTrigger value="external">External</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* System Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle>System Metrics</CardTitle>
                  <CardDescription>Current system performance indicators</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>Memory Usage</span>
                      <div className="text-right">
                        <p className="font-medium">
                          {systemMetrics.system.memory.percentage.toFixed(1)}%
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatBytes(systemMetrics.system.memory.used)} /{" "}
                          {formatBytes(systemMetrics.system.memory.total)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>CPU Usage</span>
                      <div className="text-right">
                        <p className="font-medium">{systemMetrics.system.cpu?.usage.toFixed(1)}%</p>
                        <p className="text-xs text-gray-500">Current load</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Response Time</span>
                      <div className="text-right">
                        <p className="font-medium">{systemMetrics.application.responseTime}ms</p>
                        <p className="text-xs text-gray-500">Average</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Active Connections</span>
                      <div className="text-right">
                        <p className="font-medium">{systemMetrics.application.activeConnections}</p>
                        <p className="text-xs text-gray-500">Current</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Alerts */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Alerts</CardTitle>
                  <CardDescription>Latest system alerts and notifications</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {activeAlerts.slice(0, 5).map((alert, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          {getAlertIcon(alert.type)}
                          <div>
                            <p className="font-medium text-sm">{alert.title}</p>
                            <p className="text-xs text-gray-500">
                              {new Date(alert.timestamp).toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <Badge className={getStatusColor(alert.type)}>{alert.type}</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="system" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Server className="w-8 h-8 text-blue-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Requests
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {systemMetrics.application.requestCount.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Database className="w-8 h-8 text-green-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        DB Queries
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {systemMetrics.database.queryTime}ms
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Zap className="w-8 h-8 text-purple-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Cache Hit Rate
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {systemMetrics.cache.hitRate.toFixed(1)}%
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Database Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle>Database Performance</CardTitle>
                  <CardDescription>Database connection and query metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Active Connections</span>
                      <span className="font-medium">{systemMetrics.database.connectionCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Average Query Time</span>
                      <span className="font-medium">{systemMetrics.database.queryTime}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Slow Queries</span>
                      <span className="font-medium">{systemMetrics.database.slowQueries}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Connection Errors</span>
                      <span className="font-medium">{systemMetrics.database.connectionErrors}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Cache Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle>Cache Performance</CardTitle>
                  <CardDescription>Cache usage and efficiency metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Hit Rate</span>
                      <span className="font-medium">{systemMetrics.cache.hitRate.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Memory Usage</span>
                      <span className="font-medium">
                        {formatBytes(systemMetrics.cache.memoryUsage)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Entry Count</span>
                      <span className="font-medium">
                        {systemMetrics.cache.entryCount.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Evictions</span>
                      <span className="font-medium">{systemMetrics.cache.evictions}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="alerts" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Active Alerts</CardTitle>
                <CardDescription>Current system alerts requiring attention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {activeAlerts.map((alert, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center space-x-4">
                        {getAlertIcon(alert.type)}
                        <div>
                          <div className="flex items-center space-x-2">
                            <p className="font-medium">{alert.title}</p>
                            <Badge className={getStatusColor(alert.type)}>{alert.type}</Badge>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {alert.message}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {new Date(alert.timestamp).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="w-3 h-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <BellOff className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="errors" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <XCircle className="w-8 h-8 text-red-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Total Errors
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {errorSummary.totalErrors}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <TrendingUp className="w-8 h-8 text-orange-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Error Rate
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {errorSummary.errorRate.toFixed(1)}%
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <AlertTriangle className="w-8 h-8 text-yellow-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Critical Errors
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {errorSummary.errorsByLevel.error || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Error Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Errors by Component</CardTitle>
                  <CardDescription>Error distribution across system components</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(errorSummary.errorsByComponent).map(([component, count]) => (
                      <div key={component} className="flex items-center justify-between">
                        <span className="capitalize">{component}</span>
                        <div className="text-right">
                          <p className="font-medium">{count}</p>
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-red-500 h-2 rounded-full"
                              style={{
                                width: `${(count / Math.max(...Object.values(errorSummary.errorsByComponent))) * 100}%`,
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Errors */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Errors</CardTitle>
                  <CardDescription>Latest error events and occurrences</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {errorSummary.recentErrors.map((error, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          {getAlertIcon(error.level)}
                          <div>
                            <p className="font-medium text-sm">{error.message}</p>
                            <p className="text-xs text-gray-500">
                              {error.component} • {new Date(error.timestamp).toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline">{error.occurrenceCount}x</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="logs" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <FileText className="w-8 h-8 text-blue-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Total Logs
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {logMetrics.totalLogs.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Info className="w-8 h-8 text-green-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Info Logs
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {logMetrics.logsByLevel.info?.toLocaleString() || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <AlertTriangle className="w-8 h-8 text-yellow-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Warning Logs
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {logMetrics.logsByLevel.warning?.toLocaleString() || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <XCircle className="w-8 h-8 text-red-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Error Logs
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {logMetrics.logsByLevel.error?.toLocaleString() || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Log Sources</CardTitle>
                <CardDescription>Log distribution by source and recent activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Object.entries(logMetrics.logsBySource).map(([source, count]) => (
                    <div key={source} className="text-center">
                      <p className="text-2xl font-bold text-blue-600">{count.toLocaleString()}</p>
                      <p className="text-sm text-gray-500 capitalize">{source}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="external" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* AI Providers */}
              <Card>
                <CardHeader>
                  <CardTitle>AI Providers</CardTitle>
                  <CardDescription>External AI provider status and performance</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(systemMetrics.external.aiProviders).map(
                      ([provider, status]) => (
                        <div
                          key={provider}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="flex items-center space-x-3">
                            <Globe className="w-5 h-5" />
                            <div>
                              <p className="font-medium capitalize">{provider}</p>
                              <p className="text-xs text-gray-500">
                                {status.responseTime}ms • {status.errorRate.toFixed(1)}% errors
                              </p>
                            </div>
                          </div>
                          <Badge className={getStatusColor(status.status)}>{status.status}</Badge>
                        </div>
                      )
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Third-party Services */}
              <Card>
                <CardHeader>
                  <CardTitle>Third-party Services</CardTitle>
                  <CardDescription>External service status and availability</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(systemMetrics.external.thirdPartyServices).map(
                      ([service, status]) => (
                        <div
                          key={service}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="flex items-center space-x-3">
                            <Server className="w-5 h-5" />
                            <div>
                              <p className="font-medium capitalize">{service}</p>
                              <p className="text-xs text-gray-500">
                                {status.responseTime}ms response time
                              </p>
                            </div>
                          </div>
                          <Badge className={getStatusColor(status.status)}>{status.status}</Badge>
                        </div>
                      )
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
