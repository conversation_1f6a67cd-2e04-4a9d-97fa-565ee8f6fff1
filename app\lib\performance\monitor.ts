/**
 * Performance Monitoring System
 *
 * Provides comprehensive performance monitoring including:
 * - Web Vitals tracking
 * - API response time monitoring
 * - Database query performance
 * - Memory and resource usage
 * - User experience metrics
 */

import { getVercelEnvironment } from "../vercel/environment";

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  tags?: Record<string, string>;
}

export interface WebVitalsMetric {
  name: "CLS" | "FID" | "FCP" | "LCP" | "TTFB";
  value: number;
  rating: "good" | "needs-improvement" | "poor";
  delta: number;
  id: string;
  navigationType: string;
}

export interface APIMetric {
  endpoint: string;
  method: string;
  statusCode: number;
  responseTime: number;
  timestamp: Date;
  userAgent?: string;
  userId?: string;
}

export interface ResourceMetric {
  type: "memory" | "cpu" | "disk" | "network";
  value: number;
  unit: string;
  timestamp: Date;
  threshold?: number;
  status: "normal" | "warning" | "critical";
}

/**
 * Performance metrics collector
 */
export class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private webVitals: WebVitalsMetric[] = [];
  private apiMetrics: APIMetric[] = [];
  private resourceMetrics: ResourceMetric[] = [];
  private isEnabled: boolean;

  constructor() {
    const vercelEnv = getVercelEnvironment();
    this.isEnabled = vercelEnv.isProduction || process.env.ENABLE_PERFORMANCE_MONITORING === "true";

    if (this.isEnabled) {
      this.initializeMonitoring();
    }
  }

  /**
   * Initialize performance monitoring
   */
  private initializeMonitoring() {
    // Monitor resource usage periodically
    if (typeof process !== "undefined") {
      setInterval(() => {
        this.collectResourceMetrics();
      }, 30000); // Every 30 seconds
    }

    // Clean up old metrics periodically
    setInterval(() => {
      this.cleanupOldMetrics();
    }, 300000); // Every 5 minutes
  }

  /**
   * Track Web Vitals metrics
   */
  trackWebVital(metric: WebVitalsMetric) {
    if (!this.isEnabled) return;

    this.webVitals.push(metric);

    // Log poor performance
    if (metric.rating === "poor") {
      console.warn(
        `Poor Web Vital: ${metric.name} = ${metric.value}${this.getWebVitalUnit(metric.name)}`
      );
    }

    // Send to analytics service
    this.sendToAnalytics("web-vital", {
      name: metric.name,
      value: metric.value,
      rating: metric.rating,
      navigationType: metric.navigationType,
    });
  }

  /**
   * Track API performance
   */
  trackAPICall(metric: APIMetric) {
    if (!this.isEnabled) return;

    this.apiMetrics.push(metric);

    // Log slow API calls
    if (metric.responseTime > 2000) {
      console.warn(`Slow API call: ${metric.method} ${metric.endpoint} - ${metric.responseTime}ms`);
    }

    // Log API errors
    if (metric.statusCode >= 400) {
      console.error(`API error: ${metric.method} ${metric.endpoint} - ${metric.statusCode}`);
    }

    this.sendToAnalytics("api-call", {
      endpoint: metric.endpoint,
      method: metric.method,
      statusCode: metric.statusCode,
      responseTime: metric.responseTime,
    });
  }

  /**
   * Track custom performance metric
   */
  trackMetric(name: string, value: number, unit = "ms", tags?: Record<string, string>) {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date(),
      tags,
    };

    this.metrics.push(metric);
    this.sendToAnalytics("custom-metric", metric);
  }

  /**
   * Track page load performance
   */
  trackPageLoad(route: string, loadTime: number, additionalMetrics?: Record<string, number>) {
    if (!this.isEnabled) return;

    this.trackMetric("page-load", loadTime, "ms", { route });

    if (additionalMetrics) {
      Object.entries(additionalMetrics).forEach(([key, value]) => {
        this.trackMetric(`page-load-${key}`, value, "ms", { route });
      });
    }

    // Log slow page loads
    if (loadTime > 3000) {
      console.warn(`Slow page load: ${route} - ${loadTime}ms`);
    }
  }

  /**
   * Track database query performance
   */
  trackDatabaseQuery(query: string, duration: number, success: boolean) {
    if (!this.isEnabled) return;

    const queryType = this.extractQueryType(query);

    this.trackMetric("db-query", duration, "ms", {
      type: queryType,
      success: success.toString(),
    });

    // Log slow queries
    if (duration > 1000) {
      console.warn(`Slow database query: ${queryType} - ${duration}ms`);
    }
  }

  /**
   * Collect system resource metrics
   */
  private collectResourceMetrics() {
    if (typeof process === "undefined") return;

    try {
      // Memory usage
      const memUsage = process.memoryUsage();
      const memMetric: ResourceMetric = {
        type: "memory",
        value: memUsage.heapUsed,
        unit: "bytes",
        timestamp: new Date(),
        threshold: 100 * 1024 * 1024, // 100MB
        status: memUsage.heapUsed > 100 * 1024 * 1024 ? "warning" : "normal",
      };
      this.resourceMetrics.push(memMetric);

      // CPU usage (approximation using process.cpuUsage())
      const cpuUsage = process.cpuUsage();
      const cpuMetric: ResourceMetric = {
        type: "cpu",
        value: cpuUsage.user + cpuUsage.system,
        unit: "microseconds",
        timestamp: new Date(),
        status: "normal",
      };
      this.resourceMetrics.push(cpuMetric);
    } catch (error) {
      console.error("Failed to collect resource metrics:", error);
    }
  }

  /**
   * Get performance summary
   */
  getSummary(timeRange = 3600000) {
    // Default: last hour
    const cutoff = new Date(Date.now() - timeRange);

    const recentMetrics = this.metrics.filter((m) => m.timestamp > cutoff);
    const recentAPIMetrics = this.apiMetrics.filter((m) => m.timestamp > cutoff);
    const recentWebVitals = this.webVitals.filter((m) => new Date() > cutoff);

    return {
      totalMetrics: recentMetrics.length,
      avgResponseTime: this.calculateAverage(recentAPIMetrics.map((m) => m.responseTime)),
      errorRate: this.calculateErrorRate(recentAPIMetrics),
      webVitalsRating: this.calculateWebVitalsRating(recentWebVitals),
      slowQueries: this.metrics.filter(
        (m) => m.name === "db-query" && m.value > 1000 && m.timestamp > cutoff
      ).length,
      memoryUsage: this.getLatestResourceMetric("memory"),
      timestamp: new Date(),
    };
  }

  /**
   * Get detailed metrics for analysis
   */
  getDetailedMetrics(type?: string, timeRange = 3600000) {
    const cutoff = new Date(Date.now() - timeRange);

    let metrics = this.metrics.filter((m) => m.timestamp > cutoff);

    if (type) {
      metrics = metrics.filter((m) => m.name.includes(type));
    }

    return {
      metrics,
      apiMetrics: this.apiMetrics.filter((m) => m.timestamp > cutoff),
      webVitals: this.webVitals,
      resourceMetrics: this.resourceMetrics.filter((m) => m.timestamp > cutoff),
    };
  }

  /**
   * Health check for performance monitoring
   */
  healthCheck() {
    const summary = this.getSummary();
    const memoryUsage = this.getLatestResourceMetric("memory");

    const issues = [];

    if (summary.avgResponseTime > 2000) {
      issues.push("High average response time");
    }

    if (summary.errorRate > 0.05) {
      issues.push("High error rate");
    }

    if (memoryUsage && memoryUsage.status !== "normal") {
      issues.push("High memory usage");
    }

    return {
      status: issues.length === 0 ? "healthy" : "degraded",
      issues,
      summary,
    };
  }

  /**
   * Clear old metrics to prevent memory leaks
   */
  private cleanupOldMetrics() {
    const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours

    this.metrics = this.metrics.filter((m) => m.timestamp > cutoff);
    this.apiMetrics = this.apiMetrics.filter((m) => m.timestamp > cutoff);
    this.resourceMetrics = this.resourceMetrics.filter((m) => m.timestamp > cutoff);

    // Keep web vitals for longer (they're smaller and less frequent)
    const webVitalsCutoff = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days
    this.webVitals = this.webVitals.filter((m) => new Date() > webVitalsCutoff);
  }

  /**
   * Send metrics to analytics service
   */
  private sendToAnalytics(type: string, data: any) {
    // In a real implementation, this would send to your analytics service
    // For now, we'll just log in development
    if (process.env.NODE_ENV === "development") {
      console.log(`Analytics: ${type}`, data);
    }
  }

  /**
   * Helper methods
   */
  private getWebVitalUnit(name: string): string {
    switch (name) {
      case "CLS":
        return "";
      case "FID":
        return "ms";
      case "FCP":
        return "ms";
      case "LCP":
        return "ms";
      case "TTFB":
        return "ms";
      default:
        return "";
    }
  }

  private extractQueryType(query: string): string {
    const trimmed = query.trim().toLowerCase();
    if (trimmed.startsWith("select")) return "SELECT";
    if (trimmed.startsWith("insert")) return "INSERT";
    if (trimmed.startsWith("update")) return "UPDATE";
    if (trimmed.startsWith("delete")) return "DELETE";
    return "OTHER";
  }

  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private calculateErrorRate(apiMetrics: APIMetric[]): number {
    if (apiMetrics.length === 0) return 0;
    const errors = apiMetrics.filter((m) => m.statusCode >= 400).length;
    return errors / apiMetrics.length;
  }

  private calculateWebVitalsRating(webVitals: WebVitalsMetric[]): Record<string, number> {
    const ratings = { good: 0, "needs-improvement": 0, poor: 0 };
    webVitals.forEach((wv) => {
      ratings[wv.rating]++;
    });
    return ratings;
  }

  private getLatestResourceMetric(type: string): ResourceMetric | null {
    const metrics = this.resourceMetrics
      .filter((m) => m.type === type)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    return metrics[0] || null;
  }
}

/**
 * Global performance monitor instance
 */
export const performanceMonitor = new PerformanceMonitor();

/**
 * Performance monitoring middleware for API routes
 */
export function withPerformanceMonitoring<T extends (...args: any[]) => Promise<Response>>(
  handler: T,
  endpoint: string
): T {
  return (async (...args: Parameters<T>) => {
    const startTime = Date.now();
    const request = args[0] as Request;

    try {
      const response = await handler(...args);
      const responseTime = Date.now() - startTime;

      performanceMonitor.trackAPICall({
        endpoint,
        method: request.method,
        statusCode: response.status,
        responseTime,
        timestamp: new Date(),
        userAgent: request.headers.get("user-agent") || undefined,
      });

      return response;
    } catch (error) {
      const responseTime = Date.now() - startTime;

      performanceMonitor.trackAPICall({
        endpoint,
        method: request.method,
        statusCode: 500,
        responseTime,
        timestamp: new Date(),
        userAgent: request.headers.get("user-agent") || undefined,
      });

      throw error;
    }
  }) as T;
}
