import type { MetaFunction } from "@remix-run/node";
import { <PERSON>R<PERSON>, Check, Sparkles } from "lucide-react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";

export const meta: MetaFunction = () => {
  return [
    { title: "Pricing - ChatGPT" },
    {
      name: "description",
      content: "Choose the perfect plan for your AI assistant needs. Simple, transparent pricing.",
    },
  ];
};

const plans = [
  {
    name: "Free",
    price: "$0",
    description: "Great for getting started",
    features: [
      "Limited messages per month",
      "Access to GPT-3.5",
      "Standard response time",
      "Web interface access",
      "Basic chat history",
    ],
    buttonText: "Get started",
    buttonVariant: "outline" as const,
    popular: false,
  },
  {
    name: "Plus",
    price: "$20",
    period: "/month",
    description: "Most popular for individuals",
    features: [
      "Unlimited messages",
      "Access to GPT-4",
      "Faster response times",
      "Priority access during peak times",
      "Chat history & export",
      "Advanced data analysis",
      "File uploads & image generation",
    ],
    buttonText: "Upgrade to Plus",
    buttonVariant: "default" as const,
    popular: true,
  },
  {
    name: "Team",
    price: "$25",
    period: "/month per user",
    description: "For teams and organizations",
    features: [
      "Everything in Plus",
      "Team workspace",
      "Admin controls",
      "Early access to new features",
      "Team billing",
      "Higher usage limits",
      "Priority support",
    ],
    buttonText: "Contact sales",
    buttonVariant: "outline" as const,
    popular: false,
  },
];

const faqs = [
  {
    question: "Can I cancel my subscription anytime?",
    answer:
      "Yes, you can cancel your subscription at any time. Your plan will remain active until the end of your current billing period.",
  },
  {
    question: "What's the difference between GPT-3.5 and GPT-4?",
    answer:
      "GPT-4 is our most advanced model with improved reasoning, creativity, and ability to handle complex tasks. It can also process images and has a larger context window.",
  },
  {
    question: "Do you offer educational discounts?",
    answer:
      "Yes, we offer discounts for students and educators. Please contact our support team with verification of your academic status.",
  },
  {
    question: "Can I change my plan later?",
    answer:
      "Absolutely! You can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.",
  },
  {
    question: "Is my data secure?",
    answer:
      "Yes, we take data security seriously. All conversations are encrypted, and we have strict privacy policies in place to protect your information.",
  },
];

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-background/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-6xl mx-auto flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-primary-foreground" />
            </div>
            <span className="text-xl font-semibold text-foreground">ChatGPT</span>
          </div>
          <Button variant="ghost" asChild>
            <a href="/">Back to Chat</a>
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-6 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-foreground mb-6">Choose your ChatGPT plan</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Unlock the full potential of AI assistance with our flexible pricing options.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
          {plans.map((plan, index) => (
            <Card
              key={index}
              className={`relative p-8 ${plan.popular ? "ring-2 ring-primary shadow-lg scale-105" : ""}`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                  <div className="bg-primary text-primary-foreground px-4 py-1 rounded-full text-sm font-medium">
                    Most popular
                  </div>
                </div>
              )}

              <CardContent className="p-0">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-semibold text-foreground mb-2">{plan.name}</h3>
                  <div className="mb-2">
                    <span className="text-4xl font-bold text-foreground">{plan.price}</span>
                    {plan.period && <span className="text-muted-foreground">{plan.period}</span>}
                  </div>
                  <p className="text-muted-foreground">{plan.description}</p>
                </div>

                <Button
                  variant={plan.buttonVariant}
                  className={`w-full mb-8 ${plan.popular ? "bg-primary hover:bg-primary/90 text-primary-foreground" : ""}`}
                  asChild
                >
                  <a href="/auth/register">{plan.buttonText}</a>
                </Button>

                <ul className="space-y-4">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-3">
                      <Check className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                      <span className="text-muted-foreground">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Features Comparison */}
        <section className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-2xl font-semibold text-foreground mb-4">What's included</h2>
            <p className="text-muted-foreground">
              All plans include our core features with different usage limits and capabilities.
            </p>
          </div>

          <div className="bg-muted/30 rounded-2xl p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div>
                <h3 className="font-semibold text-foreground mb-4">Core Features</h3>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    Natural language conversations
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    Creative writing assistance
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    Code generation & debugging
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    Research & analysis
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-foreground mb-4">Plus Features</h3>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    GPT-4 access
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    Image generation
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    Advanced data analysis
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    File uploads
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-foreground mb-4">Team Features</h3>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    Shared workspace
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    Admin controls
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    Team billing
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    Priority support
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ */}
        <section className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              Frequently asked questions
            </h2>
            <p className="text-muted-foreground">Have questions? We have answers.</p>
          </div>

          <div className="max-w-3xl mx-auto space-y-6">
            {faqs.map((faq, index) => (
              <Card key={index} className="p-6">
                <CardContent className="p-0">
                  <h3 className="font-semibold text-foreground mb-3">{faq.question}</h3>
                  <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* CTA */}
        <section className="text-center">
          <div className="bg-primary/5 rounded-2xl p-12">
            <h2 className="text-2xl font-semibold text-foreground mb-4">Ready to get started?</h2>
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join millions of users who are already using ChatGPT to enhance their productivity and
              creativity.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild className="bg-primary hover:bg-primary/90 text-primary-foreground">
                <a href="/auth/register" className="inline-flex items-center gap-2">
                  Start for free
                  <ArrowRight className="w-4 h-4" />
                </a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/about">Learn more</a>
              </Button>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t border-border bg-muted/20">
        <div className="max-w-6xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-primary rounded flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-primary-foreground" />
              </div>
              <span className="text-sm text-muted-foreground">ChatGPT</span>
            </div>
            <div className="flex items-center gap-6 text-sm text-muted-foreground">
              <a href="/about" className="hover:text-foreground">
                About
              </a>
              <a href="/" className="hover:text-foreground">
                Chat
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
