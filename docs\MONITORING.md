# Monitoring and Observability Guide

This guide covers monitoring, logging, and observability for the Remix + Vercel + Neon application.

## Overview

Comprehensive monitoring strategy covering:
- Application performance
- Database health
- User experience
- Error tracking
- Security monitoring

## Monitoring Stack

### Core Components

1. **Vercel Analytics** - Performance and usage metrics
2. **Neon Monitoring** - Database performance and health
3. **Application Logs** - Custom logging and debugging
4. **Health Checks** - Endpoint monitoring
5. **Error Tracking** - Error collection and analysis

## Application Monitoring

### Performance Metrics

#### Core Web Vitals

```typescript
// app/lib/monitoring/web-vitals.ts
export function trackWebVitals() {
  // Largest Contentful Paint (LCP)
  new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'largest-contentful-paint') {
        console.log('LCP:', entry.startTime);
        // Send to analytics
      }
    }
  }).observe({ entryTypes: ['largest-contentful-paint'] });

  // First Input Delay (FID)
  new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'first-input') {
        console.log('FID:', entry.processingStart - entry.startTime);
        // Send to analytics
      }
    }
  }).observe({ entryTypes: ['first-input'] });

  // Cumulative Layout Shift (CLS)
  new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
        console.log('CLS:', entry.value);
        // Send to analytics
      }
    }
  }).observe({ entryTypes: ['layout-shift'] });
}
```

#### Custom Metrics

```typescript
// app/lib/monitoring/metrics.ts
export class MetricsCollector {
  static trackPageLoad(route: string, loadTime: number) {
    console.log(`Page Load: ${route} - ${loadTime}ms`);
    // Send to analytics service
  }

  static trackAPICall(endpoint: string, duration: number, status: number) {
    console.log(`API Call: ${endpoint} - ${duration}ms - ${status}`);
    // Send to analytics service
  }

  static trackUserAction(action: string, metadata?: Record<string, any>) {
    console.log(`User Action: ${action}`, metadata);
    // Send to analytics service
  }

  static trackError(error: Error, context?: Record<string, any>) {
    console.error('Application Error:', error, context);
    // Send to error tracking service
  }
}
```

### Health Check Endpoints

```typescript
// app/routes/api.health.ts
import { json } from "@remix-run/node";
import { createDbFromEnv } from "~/lib/db";

export async function loader() {
  const checks = {
    timestamp: new Date().toISOString(),
    status: "healthy",
    checks: {
      database: { status: "unknown" },
      memory: { status: "unknown" },
      disk: { status: "unknown" },
    },
  };

  try {
    // Database health check
    const db = createDbFromEnv();
    const dbHealth = await db.healthCheck();
    checks.checks.database = {
      status: dbHealth.status,
      latency: dbHealth.latency,
      timestamp: dbHealth.timestamp,
    };

    // Memory check
    const memUsage = process.memoryUsage();
    checks.checks.memory = {
      status: memUsage.heapUsed < 100 * 1024 * 1024 ? "healthy" : "warning", // 100MB threshold
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
    };

    // Overall status
    const allHealthy = Object.values(checks.checks).every(
      check => check.status === "healthy"
    );
    checks.status = allHealthy ? "healthy" : "degraded";

  } catch (error) {
    checks.status = "unhealthy";
    checks.checks.database.status = "unhealthy";
    console.error("Health check failed:", error);
  }

  return json(checks, {
    status: checks.status === "healthy" ? 200 : 503,
    headers: {
      "Cache-Control": "no-cache, no-store, must-revalidate",
    },
  });
}
```

## Database Monitoring

### Neon Dashboard Metrics

Monitor these key metrics in Neon Console:

1. **Connection Metrics**
   - Active connections
   - Connection pool usage
   - Connection errors

2. **Query Performance**
   - Query execution time
   - Slow queries
   - Query frequency

3. **Resource Usage**
   - CPU utilization
   - Memory usage
   - Storage usage
   - I/O operations

### Custom Database Monitoring

```typescript
// app/lib/monitoring/database.ts
export class DatabaseMonitor {
  static async checkConnectionPool() {
    const db = createDbFromEnv();
    const stats = db.getConnectionStats();
    
    if (stats.activeConnections > stats.maxConnections * 0.8) {
      console.warn("High connection usage:", stats);
      // Alert if connection pool is 80% full
    }
    
    return stats;
  }

  static async trackSlowQueries() {
    // This would be implemented in the query execution layer
    const slowQueries = NeonQueryMonitor.getSlowQueries();
    
    if (slowQueries.length > 0) {
      console.warn("Slow queries detected:", slowQueries);
      // Send alerts for queries taking > 1 second
    }
    
    return slowQueries;
  }

  static async checkDatabaseSize() {
    const db = createDbFromEnv();
    const result = await db.execute(`
      SELECT 
        pg_size_pretty(pg_database_size(current_database())) as size,
        pg_database_size(current_database()) as size_bytes
    `);
    
    return result[0];
  }
}
```

## Error Tracking

### Error Boundaries

```typescript
// app/components/ErrorBoundary.tsx
import { useRouteError, isRouteErrorResponse } from "@remix-run/react";
import { MetricsCollector } from "~/lib/monitoring/metrics";

export function ErrorBoundary() {
  const error = useRouteError();

  // Track error
  if (error instanceof Error) {
    MetricsCollector.trackError(error, {
      route: window.location.pathname,
      userAgent: navigator.userAgent,
    });
  }

  if (isRouteErrorResponse(error)) {
    return (
      <div className="error-boundary">
        <h1>Oops! Something went wrong</h1>
        <p>Status: {error.status}</p>
        <p>Message: {error.statusText}</p>
      </div>
    );
  }

  return (
    <div className="error-boundary">
      <h1>Unexpected Error</h1>
      <p>We're sorry, but something went wrong.</p>
    </div>
  );
}
```

### API Error Tracking

```typescript
// app/lib/monitoring/api-errors.ts
export function trackAPIError(
  endpoint: string,
  error: Error,
  request?: Request
) {
  const errorData = {
    endpoint,
    error: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    userAgent: request?.headers.get("user-agent"),
    ip: request?.headers.get("x-forwarded-for"),
  };

  console.error("API Error:", errorData);
  
  // Send to error tracking service
  // Example: Sentry, LogRocket, etc.
}
```

## Logging Strategy

### Structured Logging

```typescript
// app/lib/monitoring/logger.ts
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export class Logger {
  private static level = process.env.NODE_ENV === "production" 
    ? LogLevel.WARN 
    : LogLevel.DEBUG;

  static debug(message: string, data?: any) {
    if (this.level <= LogLevel.DEBUG) {
      console.log(this.format("DEBUG", message, data));
    }
  }

  static info(message: string, data?: any) {
    if (this.level <= LogLevel.INFO) {
      console.log(this.format("INFO", message, data));
    }
  }

  static warn(message: string, data?: any) {
    if (this.level <= LogLevel.WARN) {
      console.warn(this.format("WARN", message, data));
    }
  }

  static error(message: string, error?: Error, data?: any) {
    if (this.level <= LogLevel.ERROR) {
      console.error(this.format("ERROR", message, { error: error?.message, stack: error?.stack, ...data }));
    }
  }

  private static format(level: string, message: string, data?: any) {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      pid: process.pid,
    });
  }
}
```

### Request Logging

```typescript
// app/lib/middleware/request-logger.ts
export function logRequest(request: Request) {
  const startTime = Date.now();
  
  return {
    end: (status: number) => {
      const duration = Date.now() - startTime;
      
      Logger.info("Request completed", {
        method: request.method,
        url: request.url,
        status,
        duration,
        userAgent: request.headers.get("user-agent"),
        ip: request.headers.get("x-forwarded-for"),
      });
    }
  };
}
```

## Alerting

### Alert Conditions

1. **Critical Alerts** (Immediate response required)
   - Application down (health check fails)
   - Database connection failures
   - High error rates (>5% in 5 minutes)
   - Payment processing failures

2. **Warning Alerts** (Monitor closely)
   - High response times (>2 seconds)
   - High database connection usage (>80%)
   - Slow queries (>1 second)
   - High memory usage (>80%)

3. **Info Alerts** (Good to know)
   - Deployment completed
   - High traffic spikes
   - New user registrations

### Alert Channels

1. **Email** - For all alerts
2. **Slack** - For critical and warning alerts
3. **SMS** - For critical alerts only
4. **Dashboard** - Real-time status display

## Dashboards

### Key Metrics Dashboard

1. **Application Health**
   - Uptime percentage
   - Response times
   - Error rates
   - Active users

2. **Database Performance**
   - Query performance
   - Connection usage
   - Storage usage
   - Backup status

3. **Business Metrics**
   - User registrations
   - Payment transactions
   - Feature usage
   - Revenue metrics

### Custom Dashboard Setup

```typescript
// app/routes/admin.monitoring.tsx
export function MonitoringDashboard() {
  const [metrics, setMetrics] = useState(null);

  useEffect(() => {
    const fetchMetrics = async () => {
      const response = await fetch("/api/admin/metrics");
      const data = await response.json();
      setMetrics(data);
    };

    fetchMetrics();
    const interval = setInterval(fetchMetrics, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="monitoring-dashboard">
      <h1>System Monitoring</h1>
      
      <div className="metrics-grid">
        <MetricCard title="Uptime" value={metrics?.uptime} />
        <MetricCard title="Response Time" value={metrics?.responseTime} />
        <MetricCard title="Error Rate" value={metrics?.errorRate} />
        <MetricCard title="Active Users" value={metrics?.activeUsers} />
      </div>
      
      <div className="charts">
        <ResponseTimeChart data={metrics?.responseTimeHistory} />
        <ErrorRateChart data={metrics?.errorRateHistory} />
      </div>
    </div>
  );
}
```

## Security Monitoring

### Security Events to Monitor

1. **Authentication Events**
   - Failed login attempts
   - Account lockouts
   - Password resets
   - Suspicious login patterns

2. **API Security**
   - Rate limit violations
   - Invalid API keys
   - Unusual request patterns
   - SQL injection attempts

3. **Data Access**
   - Unauthorized data access
   - Data export activities
   - Admin actions
   - Permission changes

### Security Logging

```typescript
// app/lib/monitoring/security.ts
export class SecurityMonitor {
  static logAuthEvent(event: string, userId?: string, metadata?: any) {
    Logger.info("Security Event", {
      type: "auth",
      event,
      userId,
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }

  static logSuspiciousActivity(activity: string, details: any) {
    Logger.warn("Suspicious Activity", {
      type: "security",
      activity,
      details,
      timestamp: new Date().toISOString(),
    });
    
    // Send immediate alert for suspicious activities
  }

  static logDataAccess(resource: string, userId: string, action: string) {
    Logger.info("Data Access", {
      type: "data_access",
      resource,
      userId,
      action,
      timestamp: new Date().toISOString(),
    });
  }
}
```

## Maintenance

### Regular Monitoring Tasks

1. **Daily**
   - Check error rates
   - Review performance metrics
   - Monitor resource usage

2. **Weekly**
   - Analyze slow queries
   - Review security logs
   - Check backup status

3. **Monthly**
   - Performance trend analysis
   - Capacity planning review
   - Alert threshold tuning

### Monitoring Tools Maintenance

1. **Log Rotation**
   - Configure log retention
   - Archive old logs
   - Monitor disk usage

2. **Metric Cleanup**
   - Remove outdated metrics
   - Optimize dashboard queries
   - Update alert thresholds

## Best Practices

1. **Monitoring Coverage**
   - Monitor all critical paths
   - Include business metrics
   - Track user experience

2. **Alert Fatigue Prevention**
   - Set appropriate thresholds
   - Use alert escalation
   - Regular alert review

3. **Data Retention**
   - Define retention policies
   - Archive historical data
   - Balance cost vs. value

4. **Documentation**
   - Document alert procedures
   - Maintain runbooks
   - Update monitoring guides

## Troubleshooting

### Common Issues

1. **High False Positive Rate**
   - Review alert thresholds
   - Add context to alerts
   - Implement alert correlation

2. **Missing Critical Events**
   - Expand monitoring coverage
   - Add redundant checks
   - Review alert conditions

3. **Performance Impact**
   - Optimize monitoring queries
   - Use sampling for high-volume events
   - Implement async logging

## Resources

- [Vercel Analytics Documentation](https://vercel.com/docs/analytics)
- [Neon Monitoring Guide](https://neon.tech/docs/monitoring)
- [Web Vitals Documentation](https://web.dev/vitals/)
- [Error Tracking Best Practices](https://docs.sentry.io/)
