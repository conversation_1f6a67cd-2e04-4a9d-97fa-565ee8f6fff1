import { describe, expect, it } from "vitest";

/**
 * Time Utilities Unit Tests
 * Tests time formatting and manipulation utilities
 */

describe("Time Utilities", () => {
  describe("Date Formatting", () => {
    it("should format dates to locale string", () => {
      const date = new Date("2024-01-15T10:30:00Z");

      const formatDate = (date: Date, locale = "en-US") => {
        return date.toLocaleDateString(locale);
      };

      const formatTime = (date: Date, locale = "en-US") => {
        return date.toLocaleTimeString(locale);
      };

      const formatDateTime = (date: Date, locale = "en-US") => {
        return date.toLocaleString(locale);
      };

      expect(formatDate(date)).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
      expect(formatTime(date)).toMatch(/\d{1,2}:\d{2}:\d{2}/);
      expect(formatDateTime(date)).toContain("2024");
    });

    it("should format relative time", () => {
      const now = new Date("2024-01-15T10:30:00Z");

      const formatRelativeTime = (date: Date, referenceDate: Date = now) => {
        const diffMs = referenceDate.getTime() - date.getTime();
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffMinutes < 1) return "just now";
        if (diffMinutes < 60) return `${diffMinutes} minutes ago`;
        if (diffHours < 24) return `${diffHours} hours ago`;
        return `${diffDays} days ago`;
      };

      const justNow = new Date("2024-01-15T10:29:30Z");
      const tenMinutesAgo = new Date("2024-01-15T10:20:00Z");
      const twoHoursAgo = new Date("2024-01-15T08:30:00Z");
      const threeDaysAgo = new Date("2024-01-12T10:30:00Z");

      expect(formatRelativeTime(justNow, now)).toBe("just now");
      expect(formatRelativeTime(tenMinutesAgo, now)).toBe("10 minutes ago");
      expect(formatRelativeTime(twoHoursAgo, now)).toBe("2 hours ago");
      expect(formatRelativeTime(threeDaysAgo, now)).toBe("3 days ago");
    });

    it("should format ISO strings", () => {
      const date = new Date("2024-01-15T10:30:00Z");

      const formatISO = (date: Date) => date.toISOString();
      const formatISODate = (date: Date) => date.toISOString().split("T")[0];
      const formatISOTime = (date: Date) => date.toISOString().split("T")[1].split(".")[0];

      expect(formatISO(date)).toBe("2024-01-15T10:30:00.000Z");
      expect(formatISODate(date)).toBe("2024-01-15");
      expect(formatISOTime(date)).toBe("10:30:00");
    });
  });

  describe("Time Calculations", () => {
    it("should calculate time differences", () => {
      const start = new Date("2024-01-15T10:00:00Z");
      const end = new Date("2024-01-15T10:30:00Z");

      const diffInMs = end.getTime() - start.getTime();
      const diffInSeconds = diffInMs / 1000;
      const diffInMinutes = diffInSeconds / 60;
      const diffInHours = diffInMinutes / 60;

      expect(diffInMs).toBe(30 * 60 * 1000); // 30 minutes in milliseconds
      expect(diffInSeconds).toBe(1800); // 30 minutes in seconds
      expect(diffInMinutes).toBe(30);
      expect(diffInHours).toBe(0.5);
    });

    it("should add time to dates", () => {
      const baseDate = new Date("2024-01-15T10:00:00Z");

      const addMinutes = (date: Date, minutes: number) => {
        return new Date(date.getTime() + minutes * 60 * 1000);
      };

      const addHours = (date: Date, hours: number) => {
        return new Date(date.getTime() + hours * 60 * 60 * 1000);
      };

      const addDays = (date: Date, days: number) => {
        return new Date(date.getTime() + days * 24 * 60 * 60 * 1000);
      };

      const after30Minutes = addMinutes(baseDate, 30);
      const after2Hours = addHours(baseDate, 2);
      const after3Days = addDays(baseDate, 3);

      expect(after30Minutes.getUTCMinutes()).toBe(30);
      expect(after2Hours.getUTCHours()).toBe(12);
      expect(after3Days.getUTCDate()).toBe(18);
    });

    it("should work with time zones", () => {
      const utcDate = new Date("2024-01-15T10:00:00Z");

      const formatInTimezone = (date: Date, timeZone: string) => {
        return date.toLocaleString("en-US", { timeZone });
      };

      // Test different timezone formatting (results may vary based on system)
      const utcString = formatInTimezone(utcDate, "UTC");
      const nyString = formatInTimezone(utcDate, "America/New_York");
      const tokyoString = formatInTimezone(utcDate, "Asia/Tokyo");

      expect(utcString).toContain("2024");
      expect(nyString).toContain("2024");
      expect(tokyoString).toContain("2024");
    });
  });

  describe("Duration Formatting", () => {
    it("should format duration in milliseconds", () => {
      const formatDuration = (ms: number) => {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
          return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        }
        if (minutes > 0) {
          return `${minutes}m ${seconds % 60}s`;
        }
        return `${seconds}s`;
      };

      expect(formatDuration(5000)).toBe("5s");
      expect(formatDuration(125000)).toBe("2m 5s");
      expect(formatDuration(3725000)).toBe("1h 2m 5s");
    });

    it("should format response time", () => {
      const formatResponseTime = (ms: number) => {
        if (ms < 1000) return `${ms}ms`;
        if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
        return `${(ms / 60000).toFixed(1)}m`;
      };

      expect(formatResponseTime(250)).toBe("250ms");
      expect(formatResponseTime(1500)).toBe("1.5s");
      expect(formatResponseTime(75000)).toBe("1.3m");
    });
  });

  describe("Business Time Logic", () => {
    it("should determine business hours", () => {
      const isBusinessHours = (date: Date, startHour = 9, endHour = 17) => {
        const hour = date.getUTCHours();
        const day = date.getUTCDay(); // 0 = Sunday, 6 = Saturday

        // Monday to Friday
        const isWeekday = day >= 1 && day <= 5;
        const isDuringHours = hour >= startHour && hour < endHour;

        return isWeekday && isDuringHours;
      };

      const mondayMorning = new Date("2024-01-15T10:00:00Z"); // Monday 10 AM
      const sundayMorning = new Date("2024-01-14T10:00:00Z"); // Sunday 10 AM
      const mondayEvening = new Date("2024-01-15T19:00:00Z"); // Monday 7 PM

      expect(isBusinessHours(mondayMorning)).toBe(true);
      expect(isBusinessHours(sundayMorning)).toBe(false);
      expect(isBusinessHours(mondayEvening)).toBe(false);
    });

    it("should calculate business days", () => {
      const addBusinessDays = (date: Date, days: number) => {
        const result = new Date(date);
        let remainingDays = days;

        while (remainingDays > 0) {
          result.setDate(result.getDate() + 1);
          const dayOfWeek = result.getDay();

          // Skip weekends (Saturday = 6, Sunday = 0)
          if (dayOfWeek !== 0 && dayOfWeek !== 6) {
            remainingDays--;
          }
        }

        return result;
      };

      const friday = new Date("2024-01-12T10:00:00Z"); // Friday
      const nextBusinessDay = addBusinessDays(friday, 1); // Should be Monday
      const threeDaysLater = addBusinessDays(friday, 3); // Should be Wednesday

      expect(nextBusinessDay.getDay()).toBe(1); // Monday
      expect(threeDaysLater.getDay()).toBe(3); // Wednesday
    });
  });

  describe("Age and Expiration", () => {
    it("should calculate age from birth date", () => {
      const calculateAge = (birthDate: Date, referenceDate: Date = new Date()) => {
        const diffMs = referenceDate.getTime() - birthDate.getTime();
        const ageYears = Math.floor(diffMs / (1000 * 60 * 60 * 24 * 365.25));
        return ageYears;
      };

      const birthDate = new Date("1990-01-15T00:00:00Z");
      const referenceDate = new Date("2024-01-15T00:00:00Z");

      expect(calculateAge(birthDate, referenceDate)).toBe(33);
    });

    it("should check if items are expired", () => {
      const isExpired = (expirationDate: Date, currentDate: Date = new Date()) => {
        return currentDate > expirationDate;
      };

      const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // Tomorrow
      const pastDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // Yesterday
      const now = new Date();

      expect(isExpired(futureDate, now)).toBe(false);
      expect(isExpired(pastDate, now)).toBe(true);
    });

    it("should calculate time until expiration", () => {
      const timeUntilExpiration = (expirationDate: Date, currentDate: Date = new Date()) => {
        const diffMs = expirationDate.getTime() - currentDate.getTime();

        if (diffMs <= 0) return "Expired";

        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

        if (diffDays > 0) return `${diffDays} days`;
        return `${diffHours} hours`;
      };

      const now = new Date("2024-01-15T10:00:00Z");
      const in2Days = new Date("2024-01-17T10:00:00Z");
      const in5Hours = new Date("2024-01-15T15:00:00Z");
      const yesterday = new Date("2024-01-14T10:00:00Z");

      expect(timeUntilExpiration(in2Days, now)).toBe("2 days");
      expect(timeUntilExpiration(in5Hours, now)).toBe("5 hours");
      expect(timeUntilExpiration(yesterday, now)).toBe("Expired");
    });
  });
});
